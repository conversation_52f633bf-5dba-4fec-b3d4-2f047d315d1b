@echo off
echo Turtle Server Shop Build Script (Simple Version)
echo This script assumes all dependencies are already installed
echo.

echo Checking Python environment...
python --version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: Python not found, please install Python first
    pause
    exit /b 1
)

echo Checking PyInstaller...
pip show pyinstaller >nul 2>&1
if not %errorlevel%==0 (
    echo Installing PyInstaller...
    pip install pyinstaller
    if not %errorlevel%==0 (
        echo ERROR: PyInstaller installation failed
        pause
        exit /b 1
    )
)

echo Cleaning old build files...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo Starting build process...
pyinstaller --onefile --name="TurtleShop" --add-data="templates;templates" --add-data="static;static" --add-data="cert;cert" --add-data="items_config.json;." --add-data="config.json;." --hidden-import=flask --hidden-import=requests --hidden-import=cryptography --hidden-import=qrcode --hidden-import=PIL --console main.py
if not %errorlevel%==0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo SUCCESS: Build completed!
echo Executable location: dist\TurtleShop.exe
echo.
echo Usage Instructions:
echo 1. Copy cert files to the same directory as the exe
echo 2. Modify config.json if needed
echo 3. Run the exe file to start the service
echo.
pause
