#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乌龟服商城统一配置模块
整合支付和商城配置，支持从JSON文件读取配置
"""

import os
import json
from typing import Dict, List, Any

# 默认充值配置常量
DEFAULT_CHARGE_TYPES = {
    'token': {
        'id': 'token',
        'name': '点券充值',
        'description': '充值游戏点券',
        'min_amount': 1,
        'max_amount': 10000,
        'rate': 1.0,
        'unit': '点券'
    },
    'gold': {
        'id': 'gold',
        'name': '金币充值',
        'description': '充值游戏金币',
        'min_amount': 1,
        'max_amount': 10000,
        'rate': 50,
        'unit': '金币'
    }
}

class Config:
    """统一配置类"""

    # 类级别的IP缓存
    _cached_ip = None
    _cache_time = 0
    _cache_duration = 300  # 缓存5分钟

    def __init__(self, config_file: str = 'config.json'):
        # 加载配置文件
        self._load_config(config_file)

    def _load_config(self, config_file: str):
        """从JSON文件加载配置"""
        try:
            # 检查配置文件是否存在
            if not os.path.exists(config_file):
                print(f"⚠️ 配置文件 {config_file} 不存在，使用默认配置")
                self._load_default_config()
                # 创建默认配置文件
                self._save_default_config(config_file)
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 加载支付配置
            payment_config = config_data.get('payment', {})
            self.APPID = payment_config.get('appid', 'OP10001076')
            self.MERCHANT_NO = payment_config.get('merchant_no', '82230207372002Z')
            self.TERM_NO = payment_config.get('term_no', 'M4430124')
            self.IS_TEST = payment_config.get('is_test', False)

            # 通知地址配置 - 支持自动获取公网IP
            self.NOTIFY_URL = self._get_smart_url(payment_config.get('notify_url'), '/payment/notify')
            self.RETURN_URL = self._get_smart_url(payment_config.get('return_url'), '/payment/return')
            self.CASHIER_NOTIFY_URL = self._get_smart_url(payment_config.get('cashier_notify_url'), '/payment/cashier_notify')

            # 证书文件配置
            self.CERT_DIR = payment_config.get('cert_dir', 'cert')
            self.PLATFORM_CERT_PROD = payment_config.get('platform_cert_prod', 'lkl-apigw-v1.cer')
            self.PLATFORM_CERT_TEST = payment_config.get('platform_cert_test', 'lkl-apigw-v2.cer')
            self.MERCHANT_CERT = payment_config.get('merchant_cert', 'api_cert.cer')
            self.MERCHANT_KEY = payment_config.get('merchant_key', 'api_private_key.pem')

            # 支付业务配置
            self.PAYMENT_ORDER_EXPIRE_MINUTES = payment_config.get('order_expire_minutes', 20)
            self.PAYMENT_REQUEST_TIMEOUT = payment_config.get('request_timeout', 30)
            self.PAYMENT_MAX_RETRIES = payment_config.get('max_retries', 3)

            # 支持的支付方式
            self.SUPPORTED_PAY_TYPES = payment_config.get('supported_pay_types', {
                'alipay': {
                    'name': '支付宝',
                    'account_type': 'ALIPAY',
                    'pay_mode': 'ALIPAY',
                    'use_cashier': True
                },
                'wechat': {
                    'name': '微信支付',
                    'account_type': 'WECHAT',
                    'pay_mode': 'WECHAT',
                    'use_cashier': True
                }
            })

            # 交易类型
            self.TRANS_TYPES = payment_config.get('trans_types', {
                'qrcode': '41',      # 扫码支付
                'jsapi': '51',       # 公众号支付
                'miniprogram': '71'  # 小程序支付
            })

            # 游戏API配置
            game_config = config_data.get('game', {})
            self.GAME_SERVER_HOST = game_config.get('server_host', '127.0.0.1')
            self.GAME_SERVER_PORT = game_config.get('server_port', 50000)
            self.API_TOKEN = game_config.get('api_token', '888888')
            self.ADD_TOKEN_PATH = game_config.get('add_token_path', '/addtoken')
            self.ADD_GOLD_PATH = game_config.get('add_gold_path', '/addgold')
            self.SEND_ITEM_PATH = game_config.get('send_item_path', '/sendmail')
            self.CHECK_PLAYER_PATH = game_config.get('check_player_path', '/checkplayer')
            self.GAME_REQUEST_TIMEOUT = game_config.get('request_timeout', 10)
            self.GAME_MAX_RETRIES = game_config.get('max_retries', 3)

            # 商城配置
            shop_config = config_data.get('shop', {})
            # 从items_config.json加载充值配置
            self.CHARGE_TYPES = self._load_charge_types_from_items_config()

            # 数据库配置
            self.DATABASE_PATH = shop_config.get('database_path', 'shop_orders.db')

            # 订单配置
            self.ORDER_EXPIRE_HOURS = shop_config.get('order_expire_hours', 24)

            # 玩家名验证规则
            self.PLAYER_NAME_MIN_LENGTH = shop_config.get('player_name_min_length', 2)
            self.PLAYER_NAME_MAX_LENGTH = shop_config.get('player_name_max_length', 20)
            self.PLAYER_NAME_PATTERN = shop_config.get('player_name_pattern', r'^[\u4e00-\u9fa5a-zA-Z0-9_]+$')

            # 应用配置
            app_config = config_data.get('app', {})
            self.SECRET_KEY = app_config.get('secret_key', 'turtle-server-shop-secret-key-2024')
            self.HOST = app_config.get('host', '0.0.0.0')
            self.PORT = app_config.get('port', 9000)
            self.DEBUG = app_config.get('debug', False)

            # 日志配置
            self.LOG_DIR = app_config.get('log_dir', 'logs')
            self.LOG_LEVEL = app_config.get('log_level', 'INFO')

            print(f"✅ 配置文件 {config_file} 加载成功")

        except Exception as e:
            print(f"❌ 加载配置文件失败: {str(e)}")
            print("使用默认配置")
            self._load_default_config()

    def _load_charge_types_from_items_config(self) -> Dict[str, Any]:
        """从items_config.json加载充值配置"""
        try:
            items_config_path = 'items_config.json'
            if os.path.exists(items_config_path):
                with open(items_config_path, 'r', encoding='utf-8') as f:
                    items_data = json.load(f)
                    charge_types = items_data.get('charge_types', {})
                    if charge_types:
                        print(f"✅ 从 {items_config_path} 加载充值配置成功")
                        return charge_types

            print(f"⚠️ {items_config_path} 中未找到充值配置，使用默认配置")
        except Exception as e:
            print(f"❌ 从items_config.json加载充值配置失败: {str(e)}")

        # 返回默认充值配置
        return DEFAULT_CHARGE_TYPES.copy()

    def _get_public_ip(self) -> str:
        """获取公网IP地址（带缓存）"""
        import time

        # 检查缓存
        current_time = time.time()
        if (Config._cached_ip and
            current_time - Config._cache_time < Config._cache_duration):
            print(f"✅ 使用缓存的公网IP: {Config._cached_ip}")
            return Config._cached_ip

        try:
            import requests

            # IP查询服务列表（按可靠性排序）
            ip_services = [
                'https://api.ipify.org',
                'https://ipinfo.io/ip',
                'https://icanhazip.com'
            ]

            for service in ip_services:
                try:
                    response = requests.get(service, timeout=3)
                    if response.status_code == 200:
                        ip = response.text.strip()
                        # 简单验证IP格式
                        if self._is_valid_ip(ip):
                            # 更新缓存
                            Config._cached_ip = ip
                            Config._cache_time = current_time
                            print(f"✅ 自动获取公网IP: {ip}")
                            return ip
                except:
                    continue

            print("⚠️ 无法获取公网IP，使用配置中的默认IP")
            return None

        except ImportError:
            print("⚠️ requests模块不可用，无法获取公网IP")
            return None
        except Exception as e:
            print(f"⚠️ 获取公网IP失败: {str(e)}")
            return None

    def _is_valid_ip(self, ip: str) -> bool:
        """验证IP地址格式"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not (0 <= int(part) <= 255):
                    return False
            return True
        except:
            return False

    def _get_smart_url(self, config_url: str, path: str) -> str:
        """智能生成URL - 如果配置为空或使用默认IP，则尝试自动获取公网IP"""
        # 如果配置文件中有明确的非默认地址，直接使用
        if config_url and '***************' not in config_url:
            return config_url

        # 尝试获取公网IP
        auto_ip = self._get_public_ip()
        if auto_ip:
            return f"http://{auto_ip}:9000{path}"

        # 如果有配置地址（即使是默认IP），使用配置地址
        if config_url:
            return config_url

        # 最后回退到默认地址
        return f"http://***************:9000{path}"

    def _load_default_config(self):
        """加载默认配置"""
        # 支付配置
        self.APPID = 'OP10001076'
        self.MERCHANT_NO = '82230207372002Z'
        self.TERM_NO = 'M4430124'
        self.IS_TEST = False
        # 尝试获取公网IP，失败则使用默认IP
        auto_ip = self._get_public_ip()
        default_base_url = f"http://{auto_ip}:9000" if auto_ip else "http://***************:9000"

        self.NOTIFY_URL = f'{default_base_url}/payment/notify'
        self.RETURN_URL = f'{default_base_url}/payment/return'
        self.CASHIER_NOTIFY_URL = f'{default_base_url}/payment/cashier_notify'
        self.CERT_DIR = 'cert'
        self.PLATFORM_CERT_PROD = 'lkl-apigw-v1.cer'
        self.PLATFORM_CERT_TEST = 'lkl-apigw-v2.cer'
        self.MERCHANT_CERT = 'api_cert.cer'
        self.MERCHANT_KEY = 'api_private_key.pem'
        self.PAYMENT_ORDER_EXPIRE_MINUTES = 20
        self.PAYMENT_REQUEST_TIMEOUT = 30
        self.PAYMENT_MAX_RETRIES = 3

        self.SUPPORTED_PAY_TYPES = {
            'alipay': {
                'name': '支付宝',
                'account_type': 'ALIPAY',
                'pay_mode': 'ALIPAY',
                'use_cashier': True
            },
            'wechat': {
                'name': '微信支付',
                'account_type': 'WECHAT',
                'pay_mode': 'WECHAT',
                'use_cashier': True
            }
        }

        self.TRANS_TYPES = {
            'qrcode': '41',
            'jsapi': '51',
            'miniprogram': '71'
        }

        # 游戏配置
        self.GAME_SERVER_HOST = '127.0.0.1'
        self.GAME_SERVER_PORT = 50000
        self.API_TOKEN = '888888'
        self.ADD_TOKEN_PATH = '/addtoken'
        self.ADD_GOLD_PATH = '/addgold'
        self.SEND_ITEM_PATH = '/sendmail'
        self.CHECK_PLAYER_PATH = '/checkplayer'
        self.GAME_REQUEST_TIMEOUT = 10
        self.GAME_MAX_RETRIES = 3

        # 商城配置
        self.CHARGE_TYPES = DEFAULT_CHARGE_TYPES.copy()

        self.DATABASE_PATH = 'shop_orders.db'
        self.ORDER_EXPIRE_HOURS = 24
        self.PLAYER_NAME_MIN_LENGTH = 2
        self.PLAYER_NAME_MAX_LENGTH = 20
        self.PLAYER_NAME_PATTERN = r'^[\u4e00-\u9fa5a-zA-Z0-9_]+$'

        # 应用配置
        self.SECRET_KEY = 'turtle-server-shop-secret-key-2024'
        self.HOST = '0.0.0.0'
        self.PORT = 9000
        self.DEBUG = False
        self.LOG_DIR = 'logs'
        self.LOG_LEVEL = 'INFO'

    def _save_default_config(self, config_file: str):
        """保存默认配置到文件"""
        try:
            default_config = {
                "payment": {
                    "appid": self.APPID,
                    "merchant_no": self.MERCHANT_NO,
                    "term_no": self.TERM_NO,
                    "is_test": self.IS_TEST,
                    "notify_url": self.NOTIFY_URL,
                    "return_url": self.RETURN_URL,
                    "cashier_notify_url": self.CASHIER_NOTIFY_URL,
                    "cert_dir": self.CERT_DIR,
                    "platform_cert_prod": self.PLATFORM_CERT_PROD,
                    "platform_cert_test": self.PLATFORM_CERT_TEST,
                    "merchant_cert": self.MERCHANT_CERT,
                    "merchant_key": self.MERCHANT_KEY,
                    "order_expire_minutes": self.PAYMENT_ORDER_EXPIRE_MINUTES,
                    "request_timeout": self.PAYMENT_REQUEST_TIMEOUT,
                    "max_retries": self.PAYMENT_MAX_RETRIES,
                    "supported_pay_types": self.SUPPORTED_PAY_TYPES,
                    "trans_types": self.TRANS_TYPES
                },
                "game": {
                    "server_host": self.GAME_SERVER_HOST,
                    "server_port": self.GAME_SERVER_PORT,
                    "api_token": self.API_TOKEN,
                    "add_token_path": self.ADD_TOKEN_PATH,
                    "add_gold_path": self.ADD_GOLD_PATH,
                    "send_item_path": self.SEND_ITEM_PATH,
                    "check_player_path": self.CHECK_PLAYER_PATH,
                    "request_timeout": self.GAME_REQUEST_TIMEOUT,
                    "max_retries": self.GAME_MAX_RETRIES
                },
                "shop": {
                    "charge_types": self.CHARGE_TYPES,
                    "database_path": self.DATABASE_PATH,
                    "order_expire_hours": self.ORDER_EXPIRE_HOURS,
                    "player_name_min_length": self.PLAYER_NAME_MIN_LENGTH,
                    "player_name_max_length": self.PLAYER_NAME_MAX_LENGTH,
                    "player_name_pattern": self.PLAYER_NAME_PATTERN
                },
                "app": {
                    "secret_key": self.SECRET_KEY,
                    "host": self.HOST,
                    "port": self.PORT,
                    "debug": self.DEBUG,
                    "log_dir": self.LOG_DIR,
                    "log_level": self.LOG_LEVEL
                }
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)

            print(f"✅ 默认配置文件已创建: {config_file}")

        except Exception as e:
            print(f"❌ 创建默认配置文件失败: {str(e)}")

    # ==================== 支付相关方法 ====================
    def get_cert_path(self, cert_type: str) -> str:
        """获取证书文件路径"""
        cert_files = {
            'platform_prod': self.PLATFORM_CERT_PROD,
            'platform_test': self.PLATFORM_CERT_TEST,
            'merchant_cert': self.MERCHANT_CERT,
            'merchant_key': self.MERCHANT_KEY
        }
        
        if cert_type not in cert_files:
            raise ValueError(f"未知的证书类型: {cert_type}")
        
        # 检查是否有以APPID命名的证书文件
        if cert_type in ['merchant_cert', 'merchant_key']:
            appid_cert = f"{self.APPID}.{'cer' if cert_type == 'merchant_cert' else 'pem'}"
            appid_path = os.path.join(self.CERT_DIR, appid_cert)
            if os.path.exists(appid_path):
                return appid_path
        
        return os.path.join(self.CERT_DIR, cert_files[cert_type])
    
    def get_supported_payment_methods(self) -> List[Dict[str, Any]]:
        """获取支持的支付方式列表"""
        return [
            {
                'id': key,
                'name': value['name'],
                'account_type': value['account_type'],
                'pay_mode': value['pay_mode'],
                'use_cashier': value['use_cashier']
            }
            for key, value in self.SUPPORTED_PAY_TYPES.items()
        ]
    
    # ==================== 商城相关方法 ====================
    def get_all_charge_types(self) -> Dict[str, Any]:
        """获取所有充值类型"""
        return self.CHARGE_TYPES
    
    def get_charge_type_by_id(self, charge_type_id: str) -> Dict[str, Any]:
        """根据ID获取充值类型"""
        return self.CHARGE_TYPES.get(charge_type_id)
    
    def calculate_charge_amount(self, charge_type: str, money_amount: float) -> int:
        """根据充值金额计算游戏币数量"""
        charge_config = self.get_charge_type_by_id(charge_type)
        if not charge_config:
            return 0
        return int(money_amount * charge_config['rate'])
    
    def validate_charge_amount(self, charge_type: str, game_amount: int) -> Dict[str, Any]:
        """验证充值数量是否合法"""
        charge_config = self.get_charge_type_by_id(charge_type)
        if not charge_config:
            return {'valid': False, 'error': '无效的充值类型'}
        
        if game_amount < charge_config['min_amount']:
            return {'valid': False, 'error': f'充值数量不能少于{charge_config["min_amount"]}{charge_config["unit"]}'}
        
        if game_amount > charge_config['max_amount']:
            return {'valid': False, 'error': f'充值数量不能超过{charge_config["max_amount"]}{charge_config["unit"]}'}
        
        return {'valid': True}
    
    def get_game_api_url(self, endpoint: str) -> str:
        """获取游戏API完整URL"""
        return f"http://{self.GAME_SERVER_HOST}:{self.GAME_SERVER_PORT}{endpoint}"
    
    # ==================== 验证方法 ====================
    def validate_payment_config(self) -> Dict[str, Any]:
        """验证支付配置"""
        errors = []
        
        # 检查必需的配置
        required_fields = ['APPID', 'MERCHANT_NO', 'TERM_NO']
        for field in required_fields:
            if not getattr(self, field):
                errors.append(f"缺少必需配置: {field}")
        
        # 检查通知URL格式
        if not self.NOTIFY_URL.startswith(('http://', 'https://')):
            errors.append("NOTIFY_URL必须是有效的HTTP/HTTPS地址")
        
        # 检查证书文件
        cert_types = ['merchant_cert', 'merchant_key']
        if self.IS_TEST:
            cert_types.append('platform_test')
        else:
            cert_types.append('platform_prod')
        
        for cert_type in cert_types:
            try:
                cert_path = self.get_cert_path(cert_type)
                if not os.path.exists(cert_path):
                    errors.append(f"证书文件不存在: {cert_path}")
            except Exception as e:
                errors.append(f"证书配置错误: {str(e)}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

# 创建全局配置实例
config = Config()

# 为了兼容性，创建别名
payment_config = config
shop_config = config
