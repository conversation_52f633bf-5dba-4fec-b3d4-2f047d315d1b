{% extends "base.html" %}

{% block title %}支付结果 - 乌龟服商城{% endblock %}

{% block content %}
<div class="result-container">
    <div class="status-icon {{ status }}">
        {% if status == 'success' %}🎉
        {% elif status == 'error' %}❌
        {% else %}⏳{% endif %}
    </div>

    {% if status == 'success' %}
        <h2>支付成功！</h2>
    {% elif status == 'error' %}
        <h2>支付失败</h2>
    {% else %}
        <h2>处理中...</h2>
    {% endif %}

    {% if status == 'success' and order %}
    <!-- 成功状态显示详细信息 -->
    <div class="success-details">
        {% if order.product_type == 'item' %}
        <!-- 物品订单显示 -->
        <div class="detail-item">
            <span class="label">购买物品：</span>
            <span class="value">{{ order.product_name }}</span>
        </div>
        <div class="detail-item">
            <span class="label">支付金额：</span>
            <span class="value">¥{{ "%.2f"|format(order.price/100) }}</span>
        </div>
        <div class="detail-item">
            <span class="label">游戏角色：</span>
            <span class="value">{{ order.player_name }}</span>
        </div>
        <div class="detail-item">
            <span class="label">状态：</span>
            <span class="value">物品已发送到邮箱</span>
        </div>
        {% else %}
        <!-- 充值订单显示 -->
        <div class="detail-item">
            <span class="label">充值类型：</span>
            <span class="value">{{ order.product_name }}</span>
        </div>
        <div class="detail-item">
            <span class="label">充值金额：</span>
            <span class="value">¥{{ "%.2f"|format(order.price/100) }}</span>
        </div>
        <div class="detail-item">
            <span class="label">获得数量：</span>
            <span class="value">{{ order.amount }}{{ '点券' if order.product_type == 'token' else '金币' }}</span>
        </div>
        <div class="detail-item">
            <span class="label">游戏角色：</span>
            <span class="value">{{ order.player_name }}</span>
        </div>
        <div class="detail-item">
            <span class="label">状态：</span>
            <span class="value">充值已完成</span>
        </div>
        {% endif %}
    </div>

    <!-- 重要提示 -->
    <div class="important-notice">
        <div class="notice-icon">⚠️</div>
        <div class="notice-title">重要提示：领取方式</div>
        <div class="notice-content">
            {% if order.product_type == 'item' and (order.product_id == '1' or order.product_id == '2') %}
            进入游戏，找到NPC综合商城，点击领取充值的点券和金币选项！
            {% elif order.product_type == 'item' %}
            进入游戏，查看邮箱即可获得购买的物品！
            {% else %}
            进入游戏，找到NPC综合商城，点击领取充值的点券和金币选项！
            {% endif %}
        </div>
    </div>
    {% elif status != 'success' %}
    <!-- 非成功状态显示消息文本 -->
    <div class="message-content">
        <div class="message-text">{{ message|safe }}</div>
    </div>
    {% endif %}

    <div class="action-buttons">
        <a href="/" class="btn btn-primary">返回首页</a>
        {% if status == 'success' %}
            <a href="/" class="btn btn-secondary">继续充值</a>
        {% elif status == 'error' %}
            <a href="/pay/{{ order_no }}" class="btn btn-secondary">重新支付</a>
        {% endif %}
    </div>

    {% if status == 'processing' %}
    <script>
        setTimeout(() => {
            window.location.reload();
        }, 5000);
    </script>
    {% endif %}
</div>
{% endblock %}

