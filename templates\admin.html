{% extends "base.html" %}

{% block title %}管理后台 - 乌龟服商城{% endblock %}

{% block content %}
<div class="admin-container">
    <h2>🛠️ 管理后台</h2>
    
    <div class="admin-section">
        <h3>📊 订单管理</h3>
        <div class="admin-buttons">
            <a href="/admin/orders" class="btn btn-primary">查看订单列表</a>
        </div>
    </div>
    
    <div class="admin-section">
        <h3>🎮 游戏API测试</h3>
        <div class="admin-buttons">
            <a href="/admin/test_game_api" class="btn btn-secondary">测试连接</a>
        </div>
        
        <div class="api-test-form">
            <h4>检查玩家</h4>
            <form action="/admin/test_check_player" method="get" target="_blank">
                <input type="text" name="name" placeholder="玩家名" value="xd" required>
                <button type="submit" class="btn btn-info">检查玩家</button>
            </form>
        </div>

        <div class="api-test-form">
            <h4>发送物品</h4>
            <form action="/admin/test_send_item" method="get" target="_blank">
                <input type="text" name="name" placeholder="玩家名" value="xd" required>
                <input type="number" name="itemid" placeholder="物品ID" value="25" required>
                <input type="number" name="amount" placeholder="数量" value="1" required>
                <button type="submit" class="btn btn-success">发送物品</button>
            </form>
        </div>
    </div>
    
    <div class="admin-section">
        <h3>📋 API接口文档</h3>
        <div class="api-docs">
            <div class="api-item">
                <h4>检查玩家</h4>
                <code>GET /admin/test_check_player?name=玩家名</code>
                <p>检查指定玩家是否存在</p>
            </div>
            
            <div class="api-item">
                <h4>发送物品</h4>
                <code>GET /admin/test_send_item?name=玩家名&itemid=物品ID&amount=数量</code>
                <p>向指定玩家发送物品</p>
            </div>
            
            <div class="api-item">
                <h4>游戏服务器API</h4>
                <ul>
                    <li><code>http://127.0.0.1:50000/checkplayer?token=888888&name=玩家名</code></li>
                    <li><code>http://127.0.0.1:50000/sendmail?token=888888&name=玩家名&amount=数量&itemid=物品ID</code></li>
                    <li><code>http://127.0.0.1:50000/addtoken?token=888888&name=玩家名&amount=数量</code></li>
                    <li><code>http://127.0.0.1:50000/addgold?token=888888&name=玩家名&amount=数量</code></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.admin-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.admin-section {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.admin-section h3 {
    margin-top: 0;
    color: #495057;
}

.admin-buttons {
    margin: 15px 0;
}

.admin-buttons .btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

.api-test-form {
    margin: 20px 0;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.api-test-form h4 {
    margin-top: 0;
    color: #6c757d;
}

.api-test-form form {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

.api-test-form input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.api-test-form input[type="text"] {
    min-width: 120px;
}

.api-test-form input[type="number"] {
    width: 80px;
}

.api-docs {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.api-item {
    margin: 20px 0;
    padding-bottom: 15px;
    border-bottom: 1px solid #f1f3f4;
}

.api-item:last-child {
    border-bottom: none;
}

.api-item h4 {
    margin: 0 0 10px 0;
    color: #495057;
}

.api-item code {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #e83e8c;
}

.api-item p {
    margin: 8px 0 0 0;
    color: #6c757d;
    font-size: 14px;
}

.api-item ul {
    margin: 10px 0;
    padding-left: 20px;
}

.api-item li {
    margin: 5px 0;
    font-size: 13px;
}
</style>
{% endblock %}
