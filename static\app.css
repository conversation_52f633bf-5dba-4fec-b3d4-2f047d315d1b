/* 乌龟服商城样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 头部样式 */
.header {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 内容区域 */
.content {
    padding: 40px;
}

.section {
    margin-bottom: 40px;
}

.section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.8em;
    border-bottom: 3px solid #ff6b6b;
    padding-bottom: 10px;
}

/* 充值类型网格 */
.charge-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.charge-type-card {
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.charge-type-card:hover {
    border-color: #ff6b6b;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255,107,107,0.2);
}

.charge-type-card.selected {
    border-color: #ff6b6b;
    background: linear-gradient(135deg, #fff5f5, #ffe0e0);
}

.charge-type-name {
    font-size: 1.6em;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}

.charge-type-desc {
    font-size: 1em;
    color: #666;
    margin-bottom: 15px;
}

.charge-type-rate {
    font-size: 1.2em;
    color: #ff6b6b;
    font-weight: bold;
    margin-bottom: 10px;
}

.charge-type-limit {
    font-size: 0.9em;
    color: #888;
}

/* 表单样式 */
.form-section {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
    margin: 30px 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #ff6b6b;
}

/* 金额输入 */
.amount-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.currency-symbol {
    position: absolute;
    left: 12px;
    color: #666;
    font-weight: bold;
    z-index: 1;
}

.amount-input-group input {
    padding-left: 35px;
}

.amount-tips {
    margin-top: 8px;
    font-size: 0.9em;
    color: #666;
    min-height: 20px;
}

.amount-tips.info {
    color: #1976d2;
}

.amount-tips.error {
    color: #e74c3c;
}

/* 支付方式 */
.payment-methods {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 20px 0;
}

.payment-method {
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method small {
    display: block;
    margin-top: 5px;
    font-size: 0.8em;
    font-weight: normal;
    color: #666;
    opacity: 0.8;
}

.payment-method:hover,
.payment-method.selected {
    border-color: #ff6b6b;
    background: #fff5f5;
}

.payment-tips {
    margin-top: 10px;
    padding: 8px 12px;
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 5px;
    color: #0c5460;
    font-size: 0.9em;
}

/* 按钮样式 */
.btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255,107,107,0.3);
}

.btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-back {
    background: #6c757d;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
    margin-top: 20px;
}

/* 支付页面样式 */
.payment-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 30px 20px;
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.order-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    flex-shrink: 0;
}

.order-info h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.order-detail {
    margin: 8px 0;
    color: #666;
    font-size: 0.95em;
}

.qr-code {
    margin: 20px 0;
    padding: 15px;
    border: 2px dashed #ddd;
    border-radius: 10px;
    background: #fafafa;
    flex-shrink: 0;
}

.qr-code p {
    margin-bottom: 10px;
    font-size: 1.0em;
    color: #333;
    font-weight: bold;
}

.qr-code img {
    max-width: 200px;
    height: auto;
    border: 1px solid #eee;
    border-radius: 8px;
    background: white;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cashier-tips {
    margin-top: 10px;
    padding: 10px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    font-size: 0.9em;
}

.cashier-tips p {
    margin: 5px 0;
    color: #856404;
}

.btn-cashier {
    display: inline-block;
    margin-top: 8px;
    padding: 8px 16px;
    background: #ff6b6b;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-size: 0.9em;
    transition: background 0.3s;
}

.btn-cashier:hover {
    background: #ff5252;
}

/* 支付页面响应式设计 */
@media (max-width: 768px) {
    .payment-container {
        padding: 20px 15px;
        margin: 10px;
        max-width: calc(100% - 20px);
        min-height: calc(100vh - 20px);
    }

    .order-info {
        padding: 12px;
        margin-bottom: 15px;
    }

    .order-detail {
        font-size: 0.9em;
        margin: 6px 0;
    }

    .qr-code {
        margin: 15px 0;
        padding: 12px;
    }

    .qr-code img {
        max-width: 180px;
    }

    .status-checking {
        margin-top: 12px;
        padding: 10px;
        font-size: 0.9em;
    }

    .payment-tips {
        margin-top: 12px;
        padding: 8px;
        font-size: 0.8em;
    }
}

@media (max-height: 700px) {
    .payment-container {
        padding: 15px;
        min-height: auto;
    }

    .order-info {
        margin-bottom: 12px;
    }

    .qr-code {
        margin: 12px 0;
    }

    .qr-code img {
        max-width: 160px;
    }
}

.payment-tips {
    color: #666;
    font-size: 0.85em;
    line-height: 1.4;
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    flex-shrink: 0;
}

.status-checking {
    margin-top: 15px;
    padding: 12px;
    background: #e3f2fd;
    border-radius: 8px;
    color: #1976d2;
    flex-shrink: 0;
}

/* 结果页面样式 */
.result-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 40px;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.status-icon {
    font-size: 4em;
    margin-bottom: 20px;
}

.status-icon.success {
    color: #27ae60;
}

.status-icon.error {
    color: #e74c3c;
}

.status-icon.processing {
    color: #f39c12;
}

.result-container h2 {
    margin-bottom: 20px;
    color: #333;
}

.message-content {
    margin: 20px 0;
}

.message-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 10px;
}

.success-details {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    text-align: left;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item .label {
    font-weight: bold;
    color: #333;
}

.detail-item .value {
    color: #27ae60;
    font-weight: bold;
}

/* 重要提示样式 */
.important-notice {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid #f39c12;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.notice-icon {
    font-size: 2em;
    margin-bottom: 10px;
}

.notice-title {
    font-size: 1.2em;
    font-weight: bold;
    color: #d68910;
    margin-bottom: 10px;
}

.notice-content {
    font-size: 1em;
    color: #7d6608;
    line-height: 1.5;
    font-weight: 500;
}

.action-buttons {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

.btn-secondary {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
}

/* 底部样式 */
.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 20px;
}

/* 导航标签页样式 */
.nav-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
    overflow: hidden;
    margin-bottom: 0;
}

.nav-tab {
    flex: 1;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #e9ecef;
    border-bottom: 3px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.nav-tab:hover {
    background: #dee2e6;
}

.nav-tab.active {
    background: white;
    border-bottom-color: #ff6b6b;
    color: #ff6b6b;
}

.tab-icon {
    font-size: 1.5em;
}

.tab-text {
    font-weight: bold;
    font-size: 1.1em;
}

/* 标签页内容 */
.tab-content {
    display: none;
    background: white;
    border-radius: 0 0 12px 12px;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

/* 充值类型卡片图标 */
.charge-type-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

/* 物品网格 */
.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 10px;
    margin-bottom: 30px;
}

.item-card {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    background: white;
}

.item-card:hover {
    border-color: #ff6b6b;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255,107,107,0.15);
}

.item-card.selected {
    border-color: #ff6b6b;
    background: linear-gradient(135deg, #fff5f5, #ffe0e0);
}

.item-icon {
    font-size: 1.5em;
    margin-bottom: 5px;
    display: block;
}

.item-name {
    font-size: 0.9em;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.2;
}

.item-description {
    font-size: 0.7em;
    color: #666;
    margin-bottom: 6px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.item-price {
    font-size: 1em;
    color: #ff6b6b;
    font-weight: bold;
    margin-bottom: 4px;
}

.item-stock {
    font-size: 0.6em;
    color: #888;
}

/* 缺货物品样式 */
.item-card.out-of-stock {
    opacity: 0.6;
    cursor: not-allowed;
}

.item-card.out-of-stock:hover {
    transform: none;
    box-shadow: none;
    border-color: #e0e0e0;
}

.item-card.out-of-stock .item-stock {
    color: #dc3545;
    font-weight: bold;
}

.item-category {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #ff6b6b;
    color: white;
    padding: 2px 4px;
    border-radius: 8px;
    font-size: 0.5em;
    font-weight: bold;
}

/* 选中物品信息 */
.selected-item-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.selected-item-icon {
    font-size: 2.5em;
}

.selected-item-details h4 {
    color: #333;
    margin-bottom: 5px;
}

.selected-item-details p {
    color: #666;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.selected-item-price {
    color: #ff6b6b;
    font-weight: bold;
    font-size: 1.2em;
}

/* 数量选择器 */
.quantity-input-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 10px 0;
}

.quantity-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 8px;
    font-size: 1.2em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    border-color: #ff6b6b;
    background: #fff5f5;
}

.quantity-input-group input {
    width: 80px;
    text-align: center;
    font-weight: bold;
    font-size: 1.1em;
}

.total-price {
    text-align: center;
    margin-top: 10px;
    font-size: 1.3em;
    color: #ff6b6b;
    font-weight: bold;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.form-actions .btn {
    flex: 1;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108,117,125,0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
    }

    .header {
        padding: 20px;
    }

    .header h1 {
        font-size: 2em;
    }

    .content {
        padding: 20px;
    }

    .charge-types {
        grid-template-columns: 1fr;
    }

    .payment-methods {
        grid-template-columns: 1fr;
    }

    .items-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .item-card {
        padding: 8px;
    }

    .item-name {
        font-size: 0.8em;
    }

    .item-description {
        font-size: 0.65em;
    }

    .item-price {
        font-size: 0.9em;
    }

    .nav-tab {
        padding: 15px 10px;
    }

    .tab-text {
        font-size: 1em;
    }

    .selected-item-info {
        flex-direction: column;
        text-align: center;
    }

    .form-actions {
        flex-direction: column;
    }
}

.important-notice {
    color: #ff4444 !important;
    font-size: 1.3em !important;
    font-weight: bold !important;
    background: #fff3cd;
    padding: 8px 12px;
    border-radius: 6px;
    border: 2px solid #ffc107;
    display: inline-block;
    margin: 10px 0;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.message-text {
    line-height: 1.6;
    white-space: pre-line;
}


