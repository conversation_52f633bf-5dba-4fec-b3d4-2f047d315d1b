#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乌龟服商城主应用
整合所有功能的单一启动文件
"""

import json
import logging
import os
from datetime import datetime
from flask import Flask, request, jsonify, render_template, redirect, url_for

# 导入核心模块
from config import config
from core import payment_processor, game_api_client, shop_manager

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(config.LOG_DIR, 'app.log'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.secret_key = config.SECRET_KEY

class ShopApplication:
    """商城应用主类"""
    
    def __init__(self, app: Flask):
        self.app = app
        self.payment_processor = payment_processor
        self.shop_manager = shop_manager
        self.game_api = game_api_client

        # 创建必要目录
        self._create_directories()

        # 注册路由
        self.register_routes()
        
        # 记录启动日志
        self.log_startup_info()

    def _create_directories(self):
        """创建必要目录"""
        os.makedirs(config.LOG_DIR, exist_ok=True)
        os.makedirs(config.CERT_DIR, exist_ok=True)

    def log_startup_info(self):
        """记录启动信息"""
        logger.info("🐢 乌龟服商城启动中...")
        logger.info(f"配置信息:")
        logger.info(f"  - 游戏服务器: {config.GAME_SERVER_HOST}:{config.GAME_SERVER_PORT}")
        logger.info(f"  - 支付商户号: {config.MERCHANT_NO}")
        logger.info(f"  - 测试模式: {config.IS_TEST}")
        logger.info(f"  - 数据库路径: {config.DATABASE_PATH}")

    def register_routes(self):
        """注册所有路由"""
        # 首页
        self.app.add_url_rule('/', 'index', self.index, methods=['GET'])
        
        # 商城API
        self.app.add_url_rule('/api/products', 'get_products', self.get_products, methods=['GET'])
        self.app.add_url_rule('/api/items', 'get_items', self.get_items, methods=['GET'])
        self.app.add_url_rule('/api/create_order', 'create_order', self.create_order, methods=['POST'])
        self.app.add_url_rule('/api/create_item_order', 'create_item_order', self.create_item_order, methods=['POST'])
        self.app.add_url_rule('/api/check_payment/<order_no>', 'check_payment', self.check_payment, methods=['GET'])
        self.app.add_url_rule('/api/check_player', 'check_player_api', self.check_player_api, methods=['GET'])
        
        # 支付相关
        self.app.add_url_rule('/pay/<order_no>', 'pay_order', self.pay_order, methods=['GET'])
        self.app.add_url_rule('/payment/notify', 'payment_notify', self.payment_notify, methods=['POST'])
        self.app.add_url_rule('/payment/cashier_notify', 'cashier_notify', self.cashier_notify, methods=['POST'])
        self.app.add_url_rule('/payment/return', 'payment_return', self.payment_return, methods=['GET'])
        
        # 管理接口
        self.app.add_url_rule('/admin', 'admin', self.admin, methods=['GET'])
        self.app.add_url_rule('/admin/orders', 'admin_orders', self.admin_orders, methods=['GET'])
        self.app.add_url_rule('/admin/test_game_api', 'test_game_api', self.test_game_api, methods=['GET'])
        
        # 结果页面
        self.app.add_url_rule('/result/<order_no>', 'result', self.result, methods=['GET'])
    
    def index(self):
        """首页"""
        try:
            # 获取充值类型
            charge_types = self.shop_manager.get_all_charge_types()

            # 获取支付方式
            payment_methods = self.payment_processor.get_supported_payment_methods()

            return render_template('index.html',
                                 charge_types=charge_types,
                                 payment_methods=payment_methods)
        except Exception as e:
            logger.error(f"首页加载失败: {str(e)}")
            return f"页面加载失败: {str(e)}", 500
    
    def get_products(self):
        """获取充值类型列表API"""
        try:
            charge_types = self.shop_manager.get_all_charge_types()
            return jsonify({'success': True, 'charge_types': charge_types})
        except Exception as e:
            logger.error(f"获取充值类型失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})
    
    def get_items(self):
        """获取物品列表API"""
        try:
            items = self.shop_manager.get_all_items()
            return jsonify({'success': True, 'items': items})
        except Exception as e:
            logger.error(f"获取物品列表失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})
    
    def check_player_api(self):
        """检查玩家是否存在API"""
        try:
            player_name = request.args.get('player_name', '').strip()
            if not player_name:
                return jsonify({'success': False, 'error': '玩家名不能为空'})
            
            # 先验证玩家名格式
            validation = self.shop_manager.validate_player_name(player_name)
            if not validation['valid']:
                return jsonify({'success': False, 'error': validation['error']})
            
            # 检查玩家是否存在
            result = self.game_api.check_player_exists(player_name)

            # 转换返回格式，添加exists字段
            if result['success']:
                return jsonify({
                    'success': True,
                    'exists': True,
                    'message': '玩家存在'
                })
            else:
                return jsonify({
                    'success': True,
                    'exists': False,
                    'message': result.get('error', '玩家不存在')
                })
            
        except Exception as e:
            logger.error(f"检查玩家失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})
    
    def create_order(self):
        """创建充值订单"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': '请求数据为空'})
            
            # 获取参数
            charge_type = data.get('charge_type')
            amount = data.get('amount', 0)
            player_name = data.get('player_name', '').strip()
            payment_method = data.get('payment_method')
            
            # 参数验证
            if not all([charge_type, amount, player_name, payment_method]):
                return jsonify({'success': False, 'error': '缺少必要参数'})
            
            try:
                amount = int(amount)
            except (ValueError, TypeError):
                return jsonify({'success': False, 'error': '充值数量必须是整数'})
            
            # 创建订单
            result = self.shop_manager.create_order(
                product_id=charge_type,
                product_type=charge_type,
                amount=amount,
                player_name=player_name,
                payment_method=payment_method
            )
            
            if result['success']:
                order = result['order']
                return jsonify({
                    'success': True,
                    'order_no': order.order_no,
                    'redirect_url': f'/pay/{order.order_no}'
                })
            else:
                return jsonify(result)
                
        except Exception as e:
            logger.error(f"创建订单失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})
    
    def create_item_order(self):
        """创建物品订单"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': '请求数据为空'})
            
            # 获取参数
            item_id = data.get('item_id')
            quantity = data.get('quantity', 1)
            player_name = data.get('player_name', '').strip()
            payment_method = data.get('payment_method')
            
            # 参数验证
            if not all([item_id, player_name, payment_method]):
                return jsonify({'success': False, 'error': '缺少必要参数'})
            
            try:
                item_id = int(item_id)
                quantity = int(quantity)
            except (ValueError, TypeError):
                return jsonify({'success': False, 'error': '物品ID和数量必须是整数'})
            
            # 获取物品信息
            item = self.shop_manager.get_item_by_id(item_id)
            if not item:
                return jsonify({'success': False, 'error': '物品不存在'})
            
            # 验证购买数量
            if quantity <= 0:
                return jsonify({'success': False, 'error': '购买数量必须大于0'})
            
            if quantity > item.get('max_purchase', 1):
                return jsonify({'success': False, 'error': f'单次最多购买{item["max_purchase"]}个'})
            
            # 计算总价（分）
            total_price = int(item['price'] * quantity * 100)
            
            # 生成订单号
            order_no = self.shop_manager.generate_order_no()
            
            # 创建物品订单（使用特殊的产品类型）
            from core import Order

            # 特殊处理充值类物品的商品名称
            if '充值' in item['name']:
                # 计算实际充值金额（元）
                money_amount = item['price'] * quantity

                if '点券' in item['name']:
                    # 点券充值：根据配置比例计算实际点券数量
                    actual_amount = config.calculate_charge_amount('token', money_amount)
                    product_name = f"点券充值 {actual_amount}"
                elif '金币' in item['name']:
                    # 金币充值：根据配置比例计算实际金币数量
                    actual_amount = config.calculate_charge_amount('gold', money_amount)
                    product_name = f"金币充值 {actual_amount}"
                else:
                    product_name = f"{item['name']} x{quantity}"
            else:
                # 普通物品使用原来的格式
                product_name = f"{item['name']} x{quantity}"

            order = Order(
                order_no=order_no,
                product_id=str(item_id),
                product_name=product_name,
                product_type='item',
                amount=quantity,
                price=total_price,
                player_name=player_name,
                payment_method=payment_method,
                extra_data=json.dumps({'item_id': item_id, 'item_name': item['name']})
            )
            
            # 保存订单到数据库
            import sqlite3
            conn = sqlite3.connect(config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO orders (
                    order_no, product_id, product_name, product_type, amount, price,
                    player_name, status, payment_method, created_at, extra_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order.order_no, order.product_id, order.product_name, order.product_type,
                order.amount, order.price, order.player_name, order.status,
                order.payment_method, order.created_at.isoformat(), order.extra_data
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"物品订单创建成功: {order_no}")
            
            return jsonify({
                'success': True,
                'order_no': order.order_no,
                'redirect_url': f'/pay/{order.order_no}'
            })
                
        except Exception as e:
            logger.error(f"创建物品订单失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})

    def pay_order(self, order_no: str):
        """支付页面"""
        try:
            # 获取订单信息
            order = self.shop_manager.get_order(order_no)
            if not order:
                return "订单不存在", 404

            if order.status != 'pending':
                return redirect(url_for('result', order_no=order_no))

            # 创建新的支付订单
            payment_result = self.payment_processor.create_payment(
                order_no=order.order_no,
                payment_method=order.payment_method,
                amount=order.price,
                player_name=order.player_name,
                request_ip=request.remote_addr or '127.0.0.1'
            )

            if not payment_result['success']:
                return f"创建支付订单失败: {payment_result['error']}", 500

            # 更新订单信息
            update_data = {}
            if payment_result.get('lakala_trade_no'):
                update_data['lakala_trade_no'] = payment_result['lakala_trade_no']
            if payment_result.get('pay_order_no'):
                update_data['pay_order_no'] = payment_result['pay_order_no']

            if update_data:
                logger.info(f"更新订单数据: {list(update_data.keys())}")
                result = self.shop_manager.update_order_status(order_no, 'pending', **update_data)
                logger.info(f"订单更新结果: {result}")

            # 统一显示支付页面（所有支付方式都使用收银台二维码模式）
            return render_template('payment.html',
                                 order=order.to_dict(),
                                 qr_code=payment_result.get('qr_code'),
                                 payment_method=order.payment_method,
                                 payment_type=payment_result.get('payment_type', 'qr'),
                                 qr_url=payment_result.get('qr_url'),
                                 fallback=payment_result.get('fallback', False))

        except Exception as e:
            logger.error(f"支付页面加载失败: {str(e)}")
            return f"支付页面加载失败: {str(e)}", 500

    def check_payment(self, order_no: str):
        """检查支付状态API"""
        try:
            # 先检查本地订单状态
            order = self.shop_manager.get_order(order_no)
            if not order:
                return jsonify({'success': False, 'error': '订单不存在'})

            if order.status in ['paid', 'completed']:
                return jsonify({
                    'success': True,
                    'paid': True,
                    'status': order.status,
                    'redirect_url': f'/result/{order_no}'
                })
            elif order.status in ['failed', 'cancelled']:
                return jsonify({
                    'success': True,
                    'paid': False,
                    'failed': True,
                    'status': order.status
                })

            # 如果订单还是pending状态，查询支付平台
            if order.lakala_trade_no:
                payment_result = self.payment_processor.query_payment_status(order_no)
                if payment_result['success'] and payment_result.get('paid'):
                    # 支付成功，更新订单状态
                    self.shop_manager.update_order_status(order_no, 'paid')
                    return jsonify({
                        'success': True,
                        'paid': True,
                        'status': 'paid',
                        'redirect_url': f'/result/{order_no}'
                    })

            return jsonify({
                'success': True,
                'paid': False,
                'status': order.status
            })

        except Exception as e:
            logger.error(f"检查支付状态失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})

    def payment_notify(self):
        """支付通知回调"""
        try:
            # 获取通知数据
            notify_data = request.get_json() or request.form.to_dict()
            logger.info(f"收到支付通知: {notify_data}")

            # 验证通知签名
            # TODO: 实现签名验证逻辑

            # 获取订单号和交易信息
            out_order_no = notify_data.get('out_order_no')
            order_trade_info = notify_data.get('order_trade_info', {})

            # 从嵌套对象中获取交易状态和交易号
            trade_status = order_trade_info.get('trade_status')
            api_trade_no = order_trade_info.get('trade_no')
            pay_order_no = notify_data.get('pay_order_no')

            logger.info(f"解析支付通知: 订单号={out_order_no}, 交易状态={trade_status}, 交易号={api_trade_no}")

            if not out_order_no:
                logger.error("支付通知缺少订单号")
                return "FAIL", 400

            # 获取订单
            order = self.shop_manager.get_order(out_order_no)
            if not order:
                logger.error(f"支付通知订单不存在: {out_order_no}")
                return "FAIL", 400

            # 检查订单状态
            if order.status != 'pending':
                logger.info(f"订单已处理: {out_order_no}, 状态: {order.status}")
                return "SUCCESS"

            # 处理支付成功 (拉卡拉成功状态是'S')
            if trade_status == 'S':
                # 更新订单为已支付
                update_data = {}
                if api_trade_no:
                    update_data['lakala_trade_no'] = api_trade_no
                if pay_order_no:
                    update_data['pay_order_no'] = pay_order_no

                self.shop_manager.update_order_status(
                    out_order_no, 'paid',
                    **update_data
                )

                # 根据订单类型选择不同的处理逻辑
                if order.product_type == 'item':
                    # 物品订单处理
                    result = self.process_item_order(order)
                else:
                    # 充值订单处理
                    result = self.process_charge_order(order)

                if result['success']:
                    self.shop_manager.update_order_status(out_order_no, 'completed')
                    logger.info(f"订单处理成功: {out_order_no}")
                else:
                    self.shop_manager.update_order_status(
                        out_order_no, 'failed',
                        error_message=result.get('error', '处理失败')
                    )
                    logger.error(f"订单处理失败: {out_order_no}, 错误: {result.get('error')}")

                return "SUCCESS"
            else:
                logger.warning(f"支付失败或其他状态: {trade_status}")
                return "SUCCESS"

        except Exception as e:
            logger.error(f"处理支付通知失败: {str(e)}")
            return "FAIL", 500

    def cashier_notify(self):
        """收银台支付通知回调"""
        return self.payment_notify()  # 使用相同的处理逻辑

    def payment_return(self):
        """支付返回页面"""
        try:
            # 记录所有URL参数
            logger.info(f"🔙 收到支付返回请求，URL参数: {dict(request.args)}")

            order_no = request.args.get('out_order_no', request.args.get('out_trade_no', ''))
            url_status = request.args.get('status', '')  # 从URL获取状态

            logger.info(f"🔙 解析参数: 订单号={order_no}, URL状态={url_status}")

            if order_no:
                # 检查订单状态
                order = self.shop_manager.get_order(order_no)
                if order:
                    # 优先使用URL传递的状态，然后使用数据库状态
                    if url_status == 'success' or order.status == 'completed':
                        status = "success"
                    elif url_status == 'failed' or order.status == 'failed':
                        status = "error"
                    else:
                        status = "processing"

                    return render_template('result.html',
                                         status=status,
                                         order_no=order_no,
                                         order=order.to_dict())
                else:
                    return render_template('result.html',
                                         status="error",
                                         order_no=order_no,
                                         order=None,
                                         error_message="订单不存在")
            else:
                return render_template('result.html',
                                     status="error",
                                     order_no="",
                                     order=None,
                                     error_message="缺少订单信息")

        except Exception as e:
            logger.error(f"支付返回处理失败: {str(e)}")
            return render_template('result.html',
                                 status="error",
                                 order_no="",
                                 order=None,
                                 error_message=f"页面加载失败: {str(e)}")

    def process_charge_order(self, order) -> dict:
        """处理充值订单"""
        try:
            # 计算实际的游戏币数量
            # order.amount 是用户输入的金额（元）
            # 需要根据配置的比例转换为实际的游戏币数量
            money_amount = order.amount  # 用户充值的金额（元）
            actual_game_amount = config.calculate_charge_amount(order.product_type, money_amount)

            logger.info(f"充值转换: 用户充值{money_amount}元 -> 实际获得{actual_game_amount}{order.product_type}")

            if order.product_type == 'token':
                result = self.game_api.add_token(order.player_name, actual_game_amount)
            elif order.product_type == 'gold':
                result = self.game_api.add_gold(order.player_name, actual_game_amount)
            else:
                return {'success': False, 'error': f'不支持的充值类型: {order.product_type}'}

            return result

        except Exception as e:
            logger.error(f"处理充值订单失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def process_item_order(self, order) -> dict:
        """处理物品订单"""
        try:
            # 解析物品信息
            extra_data = {}
            if order.extra_data:
                try:
                    extra_data = json.loads(order.extra_data)
                except json.JSONDecodeError:
                    logger.warning(f"订单额外数据解析失败: {order.order_no}")

            item_id = extra_data.get('item_id') or int(order.product_id)
            item_name = extra_data.get('item_name', '')

            # 特殊处理：如果是充值类物品，调用充值接口而不是发送物品
            if '充值' in item_name:
                logger.info(f"检测到充值类物品: {item_name}, 调用充值接口")

                # 计算实际充值金额（元）
                # order.price 是总价（分），需要转换为元
                money_amount = order.price / 100.0

                if '点券' in item_name or 'token' in item_name.lower():
                    # 点券充值：根据配置比例计算实际点券数量
                    actual_token_amount = config.calculate_charge_amount('token', money_amount)
                    logger.info(f"点券充值转换: {money_amount}元 -> {actual_token_amount}点券")
                    result = self.game_api.add_token(order.player_name, actual_token_amount)
                elif '金币' in item_name or 'gold' in item_name.lower():
                    # 金币充值：根据配置比例计算实际金币数量
                    actual_gold_amount = config.calculate_charge_amount('gold', money_amount)
                    logger.info(f"金币充值转换: {money_amount}元 -> {actual_gold_amount}金币")
                    result = self.game_api.add_gold(order.player_name, actual_gold_amount)
                else:
                    logger.warning(f"未知的充值类型: {item_name}")
                    return {'success': False, 'error': f'未知的充值类型: {item_name}'}

                return result
            else:
                # 普通物品，发送物品
                result = self.game_api.send_item(order.player_name, item_id, order.amount)
                return result

        except Exception as e:
            logger.error(f"处理物品订单失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def result(self, order_no: str):
        """结果页面"""
        try:
            order = self.shop_manager.get_order(order_no)
            if not order:
                return render_template('result.html',
                                     status='error',
                                     message='订单不存在',
                                     order_no=order_no)

            # 根据订单状态确定页面状态
            if order.status == 'completed':
                status = 'success'
                message = '支付成功，充值已完成！'
            elif order.status == 'paid':
                status = 'processing'
                message = '支付成功，正在处理充值...'
            elif order.status == 'failed':
                status = 'error'
                message = f'充值失败：{order.error_message or "未知错误"}'
            elif order.status == 'cancelled':
                status = 'error'
                message = '订单已取消'
            else:
                status = 'processing'
                message = '订单处理中，请稍候...'

            return render_template('result.html',
                                 order=order.to_dict(),
                                 status=status,
                                 message=message,
                                 order_no=order_no)

        except Exception as e:
            logger.error(f"结果页面加载失败: {str(e)}")
            return render_template('result.html',
                                 status='error',
                                 message=f'页面加载失败: {str(e)}',
                                 order_no=order_no)

    def admin(self):
        """管理后台首页"""
        try:
            return render_template('admin.html')
        except Exception as e:
            logger.error(f"管理后台加载失败: {str(e)}")
            return f"管理后台加载失败: {str(e)}", 500

    def admin_orders(self):
        """管理后台订单列表"""
        try:
            # 获取最近的订单
            orders = []
            for status in ['pending', 'paid', 'completed', 'failed', 'cancelled']:
                status_orders = self.shop_manager.get_orders_by_status(status, 20)
                orders.extend(status_orders)

            # 按创建时间排序
            orders.sort(key=lambda x: x.created_at, reverse=True)

            # 转换为字典格式
            orders_data = [order.to_dict() for order in orders[:50]]  # 最多显示50个

            return jsonify({'success': True, 'orders': orders_data})

        except Exception as e:
            logger.error(f"获取订单列表失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})

    def test_game_api(self):
        """测试游戏API连接"""
        try:
            # 测试检查玩家接口
            test_result = self.game_api.check_player_exists('test')

            return jsonify({
                'success': True,
                'game_server': f"{config.GAME_SERVER_HOST}:{config.GAME_SERVER_PORT}",
                'test_result': test_result
            })

        except Exception as e:
            logger.error(f"测试游戏API失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})

# 创建应用实例
shop_app = ShopApplication(app)

def main():
    """主启动函数"""
    print("🐢 乌龟服商城启动中...")

    # 检查必要的目录（已在ShopApplication初始化时创建）

    # 检查证书文件
    cert_files = [
        'cert/api_cert.cer',
        'cert/api_private_key.pem',
        'cert/lkl-apigw-v1.cer',
        'cert/lkl-apigw-v2.cer'
    ]

    missing_certs = []
    for cert_file in cert_files:
        if not os.path.exists(cert_file):
            missing_certs.append(cert_file)

    if missing_certs:
        print("⚠️ 缺少证书文件:")
        for cert in missing_certs:
            print(f"   - {cert}")
        print("请将拉卡拉证书文件放入 cert/ 目录")
        return

    # 清理过期订单
    try:
        expired_count = shop_manager.cleanup_expired_orders()
        if expired_count > 0:
            print(f"清理了 {expired_count} 个过期订单")
    except Exception as e:
        print(f"清理过期订单失败: {str(e)}")

    print(f"访问地址: http://localhost:{config.PORT}")
    print(f"管理接口: http://localhost:{config.PORT}/admin/orders")
    print(f"API测试: http://localhost:{config.PORT}/admin/test_game_api")
    print("按 Ctrl+C 停止服务")

    # 启动应用
    try:
        app.run(debug=config.DEBUG, host=config.HOST, port=config.PORT)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")

if __name__ == '__main__':
    main()
