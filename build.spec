# -*- mode: python ; coding: utf-8 -*-
"""
乌龟服商城 PyInstaller 打包配置文件
使用方法: pyinstaller build.spec
"""

import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

# 收集数据文件
datas = [
    # 模板文件
    (os.path.join(current_dir, 'templates'), 'templates'),
    # 静态资源
    (os.path.join(current_dir, 'static'), 'static'),
    # 物品配置
    (os.path.join(current_dir, 'items_config.json'), '.'),
    # 证书目录（如果存在）
    (os.path.join(current_dir, 'cert'), 'cert'),
]

# 收集隐藏导入
hiddenimports = [
    'flask',
    'requests',
    'cryptography',
    'qrcode',
    'PIL',
    'sqlite3',
    'json',
    'datetime',
    'logging',
    'os',
    'time',
    'base64',
    'io',
    're',
    'urllib.parse',
    'dataclasses',
    'typing',
    'cryptography.hazmat.primitives',
    'cryptography.hazmat.primitives.hashes',
    'cryptography.hazmat.primitives.serialization',
    'cryptography.hazmat.primitives.asymmetric',
    'cryptography.hazmat.primitives.asymmetric.rsa',
    'cryptography.hazmat.primitives.asymmetric.padding',
    'cryptography.hazmat.backends',
    'cryptography.x509',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'unittest',
    'doctest',
]

# 分析主程序
a = Analysis(
    ['main.py'],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 处理PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='乌龟服商城',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保留控制台窗口以显示日志
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)

# 如果需要创建目录版本，取消注释以下代码
# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='乌龟服商城'
# )
