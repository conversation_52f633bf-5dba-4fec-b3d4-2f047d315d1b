{% extends "base.html" %}

{% block title %}支付中 - 乌龟服商城{% endblock %}

{% block content %}
<div class="payment-container">
    <h2>🐢 支付确认</h2>

    <!-- 订单信息 -->
    <div class="order-info">
        <h3>订单信息</h3>
        <div class="order-detail">订单号：{{ order.order_no }}</div>
        <div class="order-detail">商品：{{ order.product_name }}</div>
        <div class="order-detail">角色：{{ order.player_name }}</div>
        <div class="order-detail">金额：¥{{ "%.2f"|format(order.price/100) }}</div>
    </div>

    <!-- 二维码支付区域 -->
    {% if qr_code %}
    <div class="qr-code">
        <p>请使用
        {% if payment_type == 'cashier_qr' %}手机扫码
        {% elif payment_method == 'alipay' %}支付宝
        {% elif payment_method == 'wechat' %}微信
        {% elif payment_method == 'unionpay' %}云闪付
        {% endif %}
        扫码支付</p>

        {% if qr_code.startswith('data:image') %}
            <img src="{{ qr_code }}" alt="支付二维码">
        {% else %}
            <img src="data:image/png;base64,{{ qr_code }}" alt="支付二维码">
        {% endif %}

        {% if payment_type == 'cashier_qr' %}
            <div class="cashier-tips">
                <p>💡 扫码后选择支付方式完成支付</p>
                {% if fallback %}
                    <p>⚠️ 如扫码无效，请点击下方链接</p>
                    <a href="{{ qr_url }}" target="_blank" class="btn-cashier">打开收银台</a>
                {% endif %}
            </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- 状态检查 -->
    <div class="status-checking">
        <div id="status-text">正在检查支付状态...</div>
        <div id="countdown">页面将在 <span id="timer">60</span> 秒后自动刷新</div>
    </div>

    <!-- 支付提示 -->
    <div class="payment-tips">
        <p>• 请在20分钟内完成支付</p>
        <p>• 支付成功后将自动充值到游戏账户</p>
        <p>• 如有问题请联系客服</p>
    </div>

    <a href="/" class="btn-back">返回首页</a>
</div>
{% endblock %}

{% block scripts %}
<script>
    let timer = 60;
    let checkInterval;
    let countdownInterval;

    // 检查支付状态
    function checkPaymentStatus() {
        fetch('/api/check_payment/{{ order.order_no }}')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.status === 'completed') {
                        document.getElementById('status-text').innerHTML =
                            '✅ 支付成功！充值已完成，正在跳转...';
                        clearInterval(checkInterval);
                        clearInterval(countdownInterval);
                        setTimeout(() => {
                            window.location.href = '/payment/return?out_trade_no={{ order.order_no }}&status=success';
                        }, 2000);
                    } else if (data.status === 'failed') {
                        document.getElementById('status-text').innerHTML =
                            '❌ 支付失败：' + (data.error || '未知错误');
                        clearInterval(checkInterval);
                        clearInterval(countdownInterval);
                    }
                }
            })
            .catch(error => {
                console.error('检查支付状态失败:', error);
            });
    }

    // 倒计时
    function updateCountdown() {
        timer--;
        document.getElementById('timer').textContent = timer;

        if (timer <= 0) {
            location.reload();
        }
    }

    // 开始检查支付状态和倒计时
    checkInterval = setInterval(checkPaymentStatus, 3000);
    countdownInterval = setInterval(updateCountdown, 1000);

    // 页面加载时立即检查一次
    checkPaymentStatus();
</script>
{% endblock %}
