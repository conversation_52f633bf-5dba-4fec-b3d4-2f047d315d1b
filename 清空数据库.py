#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空数据库工具
一键清空shop_orders.db中的所有订单数据
"""

import os
import sqlite3
import shutil
from datetime import datetime

def clear_database():
    """清空数据库"""
    db_path = 'shop_orders.db'
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    # 获取清空前信息
    size_before = os.path.getsize(db_path)
    size_before_mb = size_before / (1024 * 1024)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 统计清空前的订单
        cursor.execute('SELECT COUNT(*) FROM orders')
        total_before = cursor.fetchone()[0]
        
        cursor.execute('SELECT status, COUNT(*) FROM orders GROUP BY status')
        status_before = dict(cursor.fetchall())
        
        print(f"清空前状态:")
        print(f"  数据库大小: {size_before_mb:.2f} MB")
        print(f"  订单总数: {total_before}")
        print(f"  状态统计: {status_before}")
        
        if total_before == 0:
            print("✅ 数据库已经是空的，无需清空")
            return
        
        # 备份数据库
        backup_dir = 'db_backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = os.path.join(backup_dir, f'shop_orders_backup_{timestamp}.db')
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库已备份到: {backup_path}")
        
        # 确认清空
        print(f"\n⚠️  即将清空数据库中的所有 {total_before} 个订单！")
        confirm = input("确认清空数据库吗？(输入 'YES' 确认): ").strip()
        
        if confirm != 'YES':
            print("❌ 取消清空操作")
            return
        
        # 清空订单表
        print("\n正在清空订单数据...")
        cursor.execute('DELETE FROM orders')
        deleted_count = cursor.rowcount
        
        # 重置自增ID
        cursor.execute('DELETE FROM sqlite_sequence WHERE name="orders"')
        
        # 提交更改
        conn.commit()
        
        # 压缩数据库
        print("正在压缩数据库...")
        cursor.execute('VACUUM')
        conn.commit()
        
        print(f"✅ 已清空 {deleted_count} 个订单")
        
    finally:
        conn.close()
    
    # 获取清空后大小
    size_after = os.path.getsize(db_path)
    size_after_mb = size_after / (1024 * 1024)
    
    saved_mb = size_before_mb - size_after_mb
    
    print(f"\n" + "="*50)
    print("数据库清空完成！")
    print("="*50)
    print(f"清空前: {size_before_mb:.2f} MB, {total_before} 个订单")
    print(f"清空后: {size_after_mb:.2f} MB, 0 个订单")
    print(f"节省空间: {saved_mb:.2f} MB")
    print(f"备份文件: {backup_path}")

if __name__ == '__main__':
    print("数据库清空工具")
    print("=" * 30)
    
    try:
        clear_database()
    except Exception as e:
        print(f"❌ 清空失败: {str(e)}")
    
    input("\n按回车键退出...")
