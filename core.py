#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乌龟服商城核心模块
整合所有业务逻辑，包括支付、商城、游戏API等功能
"""

import sqlite3
import json
import re
import logging
import os
import requests
import time
import base64
import io
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
import qrcode
from PIL import Image
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend
from cryptography import x509
from config import config

logger = logging.getLogger(__name__)

# ==================== 数据模型 ====================
@dataclass
class Product:
    """商品模型"""
    id: str
    name: str
    amount: int  # 充值数量
    price: int   # 价格（分）
    type: str    # 类型：token/gold
    description: str = ""
    status: str = "active"  # active/inactive
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat() if self.created_at else None
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Product':
        """从字典创建实例"""
        if 'created_at' in data and data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)

@dataclass
class Order:
    """订单模型"""
    order_no: str
    product_id: str
    product_name: str
    product_type: str
    amount: int  # 充值数量
    price: int   # 价格（分）
    player_name: str
    status: str = "pending"  # pending/paid/completed/failed/cancelled
    payment_method: str = None
    lakala_trade_no: str = None
    pay_order_no: str = None
    created_at: datetime = None
    paid_at: datetime = None
    completed_at: datetime = None
    failed_at: datetime = None
    error_message: str = None
    extra_data: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 转换datetime字段
        datetime_fields = ['created_at', 'paid_at', 'completed_at', 'failed_at']
        for field in datetime_fields:
            if data[field]:
                data[field] = data[field].isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Order':
        """从字典创建实例"""
        datetime_fields = ['created_at', 'paid_at', 'completed_at', 'failed_at']
        for field in datetime_fields:
            if field in data and data[field]:
                data[field] = datetime.fromisoformat(data[field])
        return cls(**data)

# ==================== 拉卡拉支付客户端 ====================
class LakalaClient:
    """拉卡拉支付客户端"""
    
    def __init__(self):
        self.appid = config.APPID
        self.is_test = config.IS_TEST
        
        # 设置网关地址
        if self.is_test:
            self.gateway_url = 'https://test.wsmsd.cn/sit'
        else:
            self.gateway_url = 'https://s2.lakala.com'
        
        self.version = '3.0'
        self.schema = 'LKLAPI-SHA256withRSA'
        
        # 证书文件路径
        self.platform_cert_file = config.get_cert_path(
            'platform_test' if self.is_test else 'platform_prod'
        )
        self.merchant_cert_file = config.get_cert_path('merchant_cert')
        self.merchant_private_file = config.get_cert_path('merchant_key')
        
        # 请求和响应体（用于调试）
        self.request_body = None
        self.response_body = None
    
    def _load_private_key(self):
        """加载商户私钥"""
        try:
            with open(self.merchant_private_file, 'rb') as f:
                private_key = serialization.load_pem_private_key(
                    f.read(),
                    password=None,
                    backend=default_backend()
                )
            return private_key
        except Exception as e:
            logger.error(f"加载私钥失败: {str(e)}")
            raise
    
    def _load_platform_cert(self):
        """加载拉卡拉平台证书"""
        try:
            with open(self.platform_cert_file, 'rb') as f:
                cert_data = f.read()
                cert = x509.load_pem_x509_certificate(cert_data, default_backend())
            return cert
        except Exception as e:
            logger.error(f"加载平台证书失败: {str(e)}")
            raise
    
    def get_authorization(self, body: str) -> str:
        """生成拉卡拉Authorization头部"""
        try:
            mch_serial_no = self.get_mch_serial_no()
            nonce_str = self.random_string(12)
            timestamp = int(time.time())

            # 构建签名消息
            message = f"{self.appid}\n{mch_serial_no}\n{timestamp}\n{nonce_str}\n{body}\n"
            signature = self.rsa_private_sign(message)

            return f'{self.schema} appid="{self.appid}",serial_no="{mch_serial_no}",timestamp="{timestamp}",nonce_str="{nonce_str}",signature="{signature}"'
        except Exception as e:
            logger.error(f"生成Authorization失败: {str(e)}")
            raise

    def get_mch_serial_no(self) -> str:
        """获取商户证书序列号"""
        try:
            with open(self.merchant_cert_file, 'rb') as f:
                cert_data = f.read()

            cert = x509.load_pem_x509_certificate(cert_data, default_backend())
            serial_number = cert.serial_number
            return format(serial_number, 'X')
        except Exception as e:
            logger.error(f"读取商户证书序列号失败: {str(e)}")
            raise Exception(f"证书读取失败: {str(e)}")

    def rsa_private_sign(self, message: str) -> str:
        """RSA私钥签名"""
        try:
            private_key = self._load_private_key()
            signature = private_key.sign(
                message.encode('utf-8'),
                padding.PKCS1v15(),
                hashes.SHA256()
            )
            return base64.b64encode(signature).decode('utf-8')
        except Exception as e:
            logger.error(f"RSA签名失败: {str(e)}")
            raise Exception(f"签名失败: {str(e)}")

    def random_string(self, length: int = 12) -> str:
        """生成随机字符串"""
        import string
        import random
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    

    
    def execute(self, api_path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行API请求"""
        try:
            # 构建请求体（拉卡拉格式）
            public_params = {
                'req_time': datetime.now().strftime('%Y%m%d%H%M%S'),
                'version': self.version,
                'req_data': params
            }

            # 生成请求体
            self.request_body = json.dumps(public_params, ensure_ascii=False, separators=(',', ':'))

            # 生成Authorization头部
            authorization = self.get_authorization(self.request_body)

            # 发送请求
            url = f"{self.gateway_url}{api_path}"
            headers = {
                'Content-Type': 'application/json; charset=utf-8',
                'Authorization': authorization,
                'User-Agent': 'TurtleShop/1.0'
            }

            logger.info(f"请求URL: {url}")
            logger.debug(f"请求体: {self.request_body}")
            logger.debug(f"Authorization: {authorization}")

            response = requests.post(
                url,
                data=self.request_body.encode('utf-8'),
                headers=headers,
                timeout=config.PAYMENT_REQUEST_TIMEOUT
            )

            self.response_body = response.text
            logger.debug(f"响应内容: {self.response_body}")

            if response.status_code != 200:
                logger.error(f"HTTP错误响应: {response.status_code} - {response.text}")
                raise Exception(f"HTTP请求失败: {response.status_code}")

            result = response.json()

            # 检查业务结果
            if result.get('code') in ['BBS00000', '000000']:
                return result.get('resp_data', result)
            elif result.get('msg'):
                error_msg = result.get('msg', '未知错误')
                logger.error(f"拉卡拉API错误: {result.get('code')} - {error_msg}")
                raise Exception(f"拉卡拉API错误: {error_msg}")
            else:
                logger.error("拉卡拉API返回数据解析失败")
                raise Exception('返回数据解析失败')

        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败: {str(e)}")
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"响应解析失败: {str(e)}")
            raise Exception(f"响应解析失败: {str(e)}")
        except Exception as e:
            logger.error(f"API请求失败: {str(e)}")
            raise

# ==================== 支付处理器 ====================
class PaymentProcessor:
    """支付处理器"""

    def __init__(self):
        self.client = LakalaClient()

    def generate_qrcode(self, text: str) -> str:
        """生成二维码并返回base64编码的图片"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(text)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")

            # 转换为base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{img_str}"
        except Exception as e:
            logger.error(f"生成二维码失败: {str(e)}")
            return None

    def _calculate_expire_time(self) -> str:
        """计算订单过期时间"""
        expire_time = datetime.now() + timedelta(minutes=config.PAYMENT_ORDER_EXPIRE_MINUTES)
        return expire_time.strftime('%Y%m%d%H%M%S')

    def create_qrcode_payment(self, order_no: str, account_type: str, amount: int,
                             player_name: str = None, request_ip: str = '127.0.0.1') -> Dict[str, Any]:
        """创建扫码支付订单"""
        params = {
            'out_order_no': order_no,
            'merchant_no': config.MERCHANT_NO,
            'term_no': config.TERM_NO,
            'total_amount': str(amount),
            'order_efficient_time': self._calculate_expire_time(),
            'trans_type': config.TRANS_TYPES['qrcode'],
            'account_type': account_type,
            'notify_url': config.NOTIFY_URL,
            'order_info': order_no,  # 订单信息改为订单号
            'location_info': json.dumps({'request_ip': request_ip})
        }

        # 如果有角色名，添加到用户备注中
        if player_name:
            params['remark'] = f"角色名: {player_name}"

        try:
            logger.info(f"创建扫码支付订单: {order_no}, 金额: {amount}, 类型: {account_type}")
            result = self.client.execute('/api/v3/labs/trans/preorder', params)

            # 获取支付链接
            payment_code = result.get('acc_resp_fields', {}).get('code')
            qr_code_base64 = None

            if payment_code:
                # 将支付链接转换为二维码
                qr_code_base64 = self.generate_qrcode(payment_code)
                logger.info(f"支付链接转换为二维码: {payment_code[:50]}...")
            else:
                logger.warning("拉卡拉未返回支付链接")

            return {
                'success': True,
                'payment_type': 'qrcode',
                'lakala_trade_no': result.get('trade_no'),
                'payment_code': payment_code,  # 原始支付链接
                'qr_code': qr_code_base64,     # base64编码的二维码图片
                'expire_time': self._calculate_expire_time()
            }
        except Exception as e:
            logger.error(f"创建扫码支付订单失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def create_cashier_payment(self, order_no: str, amount: int, player_name: str = None, pay_mode: str = 'ALL') -> Dict[str, Any]:
        """创建收银台支付订单"""
        expire_time = datetime.now() + timedelta(minutes=config.PAYMENT_ORDER_EXPIRE_MINUTES)

        # 构建带订单号的回调地址，直接跳转到结果页面
        callback_url = f"{config.RETURN_URL.replace('/payment/return', '/result')}/{order_no}"

        params = {
            'out_order_no': order_no,
            'merchant_no': config.MERCHANT_NO,
            'total_amount': str(amount),
            'order_efficient_time': expire_time.strftime('%Y%m%d%H%M%S'),
            'notify_url': config.CASHIER_NOTIFY_URL,
            'support_refund': 1,
            'callback_url': callback_url,
            'order_info': order_no,  # 订单信息改为订单号
            'counter_param': json.dumps({'pay_mode': pay_mode})
        }

        # 如果有角色名，添加到用户备注中
        if player_name:
            params['remark'] = f"角色名: {player_name}"

        try:
            logger.info(f"创建收银台支付订单: {order_no}, 金额: {amount}, 支付方式: {pay_mode}")
            result = self.client.execute('/api/v3/ccss/counter/order/special_create', params)

            counter_url = result.get('counter_url')
            if not counter_url:
                raise Exception("拉卡拉未返回收银台地址")

            return {
                'success': True,
                'payment_type': 'cashier',
                'lakala_trade_no': result.get('trade_no'),
                'counter_url': counter_url,
                'expire_time': expire_time.strftime('%Y%m%d%H%M%S')
            }
        except Exception as e:
            logger.error(f"创建收银台支付订单失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_cashier_qrcode(self, counter_url: str) -> Dict[str, Any]:
        """获取收银台二维码"""
        try:
            # 直接将收银台URL转换为二维码
            qr_code = self.generate_qrcode(counter_url)
            if qr_code:
                return {
                    'success': True,
                    'qr_code': qr_code,
                    'qr_url': counter_url,
                    'fallback': False
                }
            else:
                return {
                    'success': False,
                    'error': '二维码生成失败',
                    'fallback': True
                }
        except Exception as e:
            logger.error(f"获取收银台二维码失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'fallback': True
            }

    def create_payment(self, order_no: str, payment_method: str, amount: int,
                      player_name: str = None, request_ip: str = '127.0.0.1') -> Dict[str, Any]:
        """创建支付订单（统一入口）"""
        if payment_method == 'cashier':
            return self.create_cashier_payment(order_no, amount, player_name)

        # 检查是否强制使用收银台
        payment_config_item = config.SUPPORTED_PAY_TYPES.get(payment_method, {})
        if payment_config_item.get('use_cashier', False):
            logger.info(f"支付方式 {payment_method} 配置为使用收银台模式")
            pay_mode = payment_config_item.get('pay_mode', 'ALL')

            # 创建收银台支付
            cashier_result = self.create_cashier_payment(order_no, amount, player_name, pay_mode)
            if cashier_result['success']:
                # 获取收银台二维码
                qr_result = self.get_cashier_qrcode(cashier_result['counter_url'])
                if qr_result['success']:
                    # 合并结果
                    cashier_result.update({
                        'qr_code': qr_result['qr_code'],
                        'payment_type': 'cashier_qr',
                        'qr_url': qr_result.get('qr_url'),
                        'fallback': qr_result.get('fallback', False)
                    })
                    logger.info(f"收银台二维码获取成功: {payment_method}")
                else:
                    logger.warning(f"收银台二维码获取失败，保持原有跳转模式: {qr_result.get('error')}")

            return cashier_result

        # 获取账户类型映射
        account_type_map = {
            'alipay': 'ALIPAY',
            'wechat': 'WECHAT',
            'unionpay': 'UQRCODEPAY'
        }

        account_type = account_type_map.get(payment_method)
        if not account_type:
            return {
                'success': False,
                'error': f'不支持的支付方式: {payment_method}'
            }

        # 尝试创建扫码支付
        result = self.create_qrcode_payment(order_no, account_type, amount, player_name, request_ip)

        # 如果扫码支付失败且是权限问题，自动降级到收银台
        if not result['success'] and ('BBS16111' in result.get('error', '') or '未开通支付业务' in result.get('error', '')):
            logger.warning(f"扫码支付权限问题，自动降级到收银台: {payment_method}")
            pay_mode = payment_config_item.get('pay_mode', 'ALL')
            return self.create_cashier_payment(order_no, amount, player_name, pay_mode)

        return result

    def get_supported_payment_methods(self) -> List[Dict[str, Any]]:
        """获取支持的支付方式"""
        return config.get_supported_payment_methods()

    def query_payment_status(self, order_no: str) -> Dict[str, Any]:
        """查询支付状态"""
        params = {
            'out_order_no': order_no,
            'merchant_no': config.MERCHANT_NO
        }

        try:
            result = self.client.execute('/api/v3/labs/query/tradequery', params)

            trade_status = result.get('trade_status')
            if trade_status == 'SUCCESS':
                return {
                    'success': True,
                    'paid': True,
                    'trade_no': result.get('trade_no'),
                    'pay_time': result.get('pay_time')
                }
            elif trade_status in ['PROCESSING', 'WAIT_BUYER_PAY']:
                return {
                    'success': True,
                    'paid': False,
                    'status': trade_status
                }
            else:
                return {
                    'success': True,
                    'paid': False,
                    'failed': True,
                    'status': trade_status
                }
        except Exception as e:
            logger.error(f"查询支付状态失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

# ==================== 游戏API客户端 ====================
class GameAPIClient:
    """游戏API客户端"""

    def __init__(self):
        self.base_url = f"http://{config.GAME_SERVER_HOST}:{config.GAME_SERVER_PORT}"
        self.token = config.API_TOKEN
        self.timeout = config.GAME_REQUEST_TIMEOUT
        self.max_retries = config.GAME_MAX_RETRIES

    def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送HTTP请求到游戏服务器"""
        url = f"{self.base_url}{endpoint}"

        # 添加token到参数中
        params['token'] = self.token

        for attempt in range(self.max_retries):
            try:
                logger.info(f"游戏API请求 (尝试 {attempt + 1}/{self.max_retries}): {url}")
                logger.debug(f"请求参数: {params}")

                response = requests.get(url, params=params, timeout=self.timeout)

                logger.debug(f"响应状态码: {response.status_code}")
                logger.debug(f"响应内容: {response.text}")

                if response.status_code == 200:
                    try:
                        result = response.json()
                        logger.info(f"游戏API响应: {result}")
                        return result
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，尝试解析文本响应
                        text = response.text.strip()
                        if text.lower() in ['ok', 'success', '1', 'true']:
                            return {'success': True, 'message': text}
                        elif '玩家存在:' in text:
                            # 特殊处理玩家存在的响应
                            return {'success': True, 'message': text}
                        elif '玩家不存在:' in text:
                            # 特殊处理玩家不存在的响应
                            return {'success': False, 'error': text}
                        elif '充值' in text and '成功' in text:
                            # 特殊处理充值成功的响应
                            return {'success': True, 'message': text}
                        elif '发送' in text and '成功' in text:
                            # 特殊处理物品发送成功的响应
                            return {'success': True, 'message': text}
                        else:
                            return {'success': False, 'error': f'游戏服务器返回: {text}'}
                else:
                    logger.warning(f"HTTP状态码错误: {response.status_code}")
                    if attempt < self.max_retries - 1:
                        time.sleep(1)  # 重试前等待1秒
                        continue
                    else:
                        return {'success': False, 'error': f'HTTP错误: {response.status_code}'}

            except requests.exceptions.Timeout:
                logger.warning(f"请求超时 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(2)  # 超时后等待2秒再重试
                    continue
                else:
                    return {'success': False, 'error': '请求超时'}

            except requests.exceptions.ConnectionError:
                logger.warning(f"连接失败 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(2)  # 连接失败后等待2秒再重试
                    continue
                else:
                    return {'success': False, 'error': '无法连接到游戏服务器'}

            except Exception as e:
                logger.error(f"请求异常 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
                    continue
                else:
                    return {'success': False, 'error': f'请求异常: {str(e)}'}

        return {'success': False, 'error': '所有重试都失败了'}

    def check_player_exists(self, player_name: str) -> Dict[str, Any]:
        """检查玩家是否存在"""
        if not player_name or not player_name.strip():
            return {'success': False, 'error': '玩家名不能为空'}

        params = {'name': player_name.strip()}
        result = self._make_request(config.CHECK_PLAYER_PATH, params)

        # 记录检查日志
        if result['success']:
            logger.info(f"玩家存在性检查成功: {player_name}")
        else:
            logger.warning(f"玩家存在性检查失败: {player_name}, 错误={result.get('error')}")

        return result

    def add_token(self, player_name: str, amount: int) -> Dict[str, Any]:
        """充值点券"""
        if not player_name or not player_name.strip():
            return {'success': False, 'error': '玩家名不能为空'}

        if amount <= 0:
            return {'success': False, 'error': '充值数量必须大于0'}

        params = {
            'name': player_name.strip(),
            'amount': amount
        }

        result = self._make_request(config.ADD_TOKEN_PATH, params)

        # 记录充值日志
        if result['success']:
            logger.info(f"点券充值成功: 玩家={player_name}, 数量={amount}")
        else:
            logger.error(f"点券充值失败: 玩家={player_name}, 数量={amount}, 错误={result.get('error')}")

        return result

    def add_gold(self, player_name: str, amount: int) -> Dict[str, Any]:
        """充值金币"""
        if not player_name or not player_name.strip():
            return {'success': False, 'error': '玩家名不能为空'}

        if amount <= 0:
            return {'success': False, 'error': '充值数量必须大于0'}

        params = {
            'name': player_name.strip(),
            'amount': amount
        }

        result = self._make_request(config.ADD_GOLD_PATH, params)

        # 记录充值日志
        if result['success']:
            logger.info(f"金币充值成功: 玩家={player_name}, 数量={amount}")
        else:
            logger.error(f"金币充值失败: 玩家={player_name}, 数量={amount}, 错误={result.get('error')}")

        return result

    def send_item(self, player_name: str, item_id: int, amount: int) -> Dict[str, Any]:
        """发送物品给玩家"""
        if not player_name or not player_name.strip():
            return {'success': False, 'error': '玩家名不能为空'}

        if amount <= 0:
            return {'success': False, 'error': '物品数量必须大于0'}

        if item_id <= 0:
            return {'success': False, 'error': '物品ID必须大于0'}

        params = {
            'name': player_name.strip(),
            'amount': amount,
            'itemid': item_id
        }

        result = self._make_request(config.SEND_ITEM_PATH, params)

        # 记录发送物品日志
        if result['success']:
            logger.info(f"物品发送成功: 玩家={player_name}, 物品ID={item_id}, 数量={amount}")
        else:
            logger.error(f"物品发送失败: 玩家={player_name}, 物品ID={item_id}, 数量={amount}, 错误={result.get('error')}")

        return result

# ==================== 商城管理器 ====================
class ShopManager:
    """商城管理器"""

    def __init__(self, db_path: str = None):
        self.db_path = db_path or config.DATABASE_PATH
        self.items_config = None
        self.init_database()
        self.load_items_config()

    def init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建订单表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_no TEXT UNIQUE NOT NULL,
                    product_id TEXT NOT NULL,
                    product_name TEXT NOT NULL,
                    product_type TEXT NOT NULL,
                    amount INTEGER NOT NULL,
                    price INTEGER NOT NULL,
                    player_name TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    payment_method TEXT,
                    lakala_trade_no TEXT,
                    pay_order_no TEXT,
                    created_at TEXT NOT NULL,
                    paid_at TEXT,
                    completed_at TEXT,
                    failed_at TEXT,
                    error_message TEXT,
                    extra_data TEXT
                )
            ''')

            conn.commit()
            conn.close()
            logger.info("数据库初始化完成")

        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise

    def load_items_config(self):
        """加载物品配置"""
        try:
            # 查找items_config.json文件
            possible_paths = [
                'items_config.json',
                'shop/items_config.json',
                os.path.join(os.path.dirname(__file__), 'items_config.json'),
                os.path.join(os.path.dirname(__file__), 'shop', 'items_config.json')
            ]

            config_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    break

            if config_path:
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.items_config = json.load(f)
                logger.info(f"物品配置加载成功，共 {len(self.items_config.get('items', []))} 个物品")
            else:
                logger.warning("物品配置文件不存在，使用空配置")
                self.items_config = {'items': [], 'categories': []}
        except Exception as e:
            logger.error(f"加载物品配置失败: {str(e)}")
            self.items_config = {'items': [], 'categories': []}

    def get_all_items(self) -> List[Dict[str, Any]]:
        """获取所有物品"""
        if not self.items_config:
            return []
        return self.items_config.get('items', [])

    def get_item_by_id(self, item_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取物品"""
        items = self.get_all_items()
        for item in items:
            if item.get('id') == item_id:
                return item
        return None

    def get_all_charge_types(self) -> Dict[str, Any]:
        """获取所有充值类型"""
        # 优先从items_config.json获取充值配置
        if self.items_config and 'charge_types' in self.items_config:
            return self.items_config['charge_types']
        # 回退到config.py的配置
        return config.get_all_charge_types()

    def validate_player_name(self, player_name: str) -> Dict[str, Any]:
        """验证玩家名"""
        if not player_name or not player_name.strip():
            return {'valid': False, 'error': '玩家名不能为空'}

        player_name = player_name.strip()

        if len(player_name) < config.PLAYER_NAME_MIN_LENGTH:
            return {'valid': False, 'error': f'玩家名长度不能少于{config.PLAYER_NAME_MIN_LENGTH}个字符'}

        if len(player_name) > config.PLAYER_NAME_MAX_LENGTH:
            return {'valid': False, 'error': f'玩家名长度不能超过{config.PLAYER_NAME_MAX_LENGTH}个字符'}

        if not re.match(config.PLAYER_NAME_PATTERN, player_name):
            return {'valid': False, 'error': '玩家名只能包含中文、英文、数字和下划线'}

        return {'valid': True}

    def generate_order_no(self) -> str:
        """生成订单号"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        import random
        random_suffix = str(random.randint(1000, 9999))
        return f"TS{timestamp}{random_suffix}"

    def create_order(self, product_id: str, product_type: str, amount: int,
                    player_name: str, payment_method: str) -> Dict[str, Any]:
        """创建订单"""
        try:
            # 验证玩家名
            validation = self.validate_player_name(player_name)
            if not validation['valid']:
                return {'success': False, 'error': validation['error']}

            # 验证充值类型和数量
            if product_type in ['token', 'gold']:
                charge_validation = config.validate_charge_amount(product_type, amount)
                if not charge_validation['valid']:
                    return {'success': False, 'error': charge_validation['error']}

                # 计算价格（分）
                charge_config = config.get_charge_type_by_id(product_type)
                price = int(amount / charge_config['rate'] * 100)  # 转换为分

                # 计算实际获得的游戏币数量
                actual_game_amount = config.calculate_charge_amount(product_type, amount)

                # 设置商品名称为实际获得的数量
                if product_type == 'token':
                    product_name = f"点券充值 {actual_game_amount}"
                elif product_type == 'gold':
                    product_name = f"金币充值 {actual_game_amount}"
                else:
                    product_name = f"{charge_config['name']} {actual_game_amount}"
            else:
                return {'success': False, 'error': '不支持的产品类型'}

            # 生成订单号
            order_no = self.generate_order_no()

            # 创建订单对象
            order = Order(
                order_no=order_no,
                product_id=product_id,
                product_name=product_name,
                product_type=product_type,
                amount=amount,
                price=price,
                player_name=player_name.strip(),
                payment_method=payment_method
            )

            # 保存到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO orders (
                    order_no, product_id, product_name, product_type, amount, price,
                    player_name, status, payment_method, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order.order_no, order.product_id, order.product_name, order.product_type,
                order.amount, order.price, order.player_name, order.status,
                order.payment_method, order.created_at.isoformat()
            ))

            conn.commit()
            conn.close()

            logger.info(f"订单创建成功: {order_no}")
            return {'success': True, 'order': order}

        except Exception as e:
            logger.error(f"创建订单失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_order(self, order_no: str) -> Optional[Order]:
        """获取订单"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM orders WHERE order_no = ?', (order_no,))
            row = cursor.fetchone()
            conn.close()

            if row:
                # 将数据库行转换为Order对象
                order_data = {
                    'order_no': row[1],
                    'product_id': row[2],
                    'product_name': row[3],
                    'product_type': row[4],
                    'amount': row[5],
                    'price': row[6],
                    'player_name': row[7],
                    'status': row[8],
                    'payment_method': row[9],
                    'lakala_trade_no': row[10],
                    'pay_order_no': row[11],
                    'created_at': row[12],
                    'paid_at': row[13],
                    'completed_at': row[14],
                    'failed_at': row[15],
                    'error_message': row[16],
                    'extra_data': row[17]
                }
                return Order.from_dict(order_data)

            return None

        except Exception as e:
            logger.error(f"获取订单失败: {str(e)}")
            return None

    def update_order_status(self, order_no: str, status: str, **kwargs) -> bool:
        """更新订单状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建更新字段
            update_fields = ['status = ?']
            update_values = [status]

            # 添加时间戳
            current_time = datetime.now().isoformat()
            if status == 'paid':
                update_fields.append('paid_at = ?')
                update_values.append(current_time)
            elif status == 'completed':
                update_fields.append('completed_at = ?')
                update_values.append(current_time)
            elif status == 'failed':
                update_fields.append('failed_at = ?')
                update_values.append(current_time)

            # 添加其他字段
            for key, value in kwargs.items():
                if key in ['lakala_trade_no', 'pay_order_no', 'error_message', 'extra_data']:
                    update_fields.append(f'{key} = ?')
                    update_values.append(value)

            update_values.append(order_no)

            sql = f"UPDATE orders SET {', '.join(update_fields)} WHERE order_no = ?"
            cursor.execute(sql, update_values)

            success = cursor.rowcount > 0
            conn.commit()
            conn.close()

            if success:
                logger.info(f"订单状态更新成功: {order_no} -> {status}")
            else:
                logger.warning(f"订单状态更新失败，订单不存在: {order_no}")

            return success

        except Exception as e:
            logger.error(f"更新订单状态失败: {str(e)}")
            return False

    def get_orders_by_status(self, status: str, limit: int = 100) -> List[Order]:
        """根据状态获取订单列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM orders
                WHERE status = ?
                ORDER BY created_at DESC
                LIMIT ?
            ''', (status, limit))

            rows = cursor.fetchall()
            conn.close()

            orders = []
            for row in rows:
                order_data = {
                    'order_no': row[1],
                    'product_id': row[2],
                    'product_name': row[3],
                    'product_type': row[4],
                    'amount': row[5],
                    'price': row[6],
                    'player_name': row[7],
                    'status': row[8],
                    'payment_method': row[9],
                    'lakala_trade_no': row[10],
                    'pay_order_no': row[11],
                    'created_at': row[12],
                    'paid_at': row[13],
                    'completed_at': row[14],
                    'failed_at': row[15],
                    'error_message': row[16],
                    'extra_data': row[17]
                }
                orders.append(Order.from_dict(order_data))

            return orders

        except Exception as e:
            logger.error(f"获取订单列表失败: {str(e)}")
            return []

    def cleanup_expired_orders(self) -> int:
        """清理过期订单"""
        try:
            expire_time = datetime.now() - timedelta(hours=config.ORDER_EXPIRE_HOURS)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE orders
                SET status = 'cancelled'
                WHERE status = 'pending'
                AND created_at < ?
            ''', (expire_time.isoformat(),))

            cancelled_count = cursor.rowcount
            conn.commit()
            conn.close()

            if cancelled_count > 0:
                logger.info(f"清理过期订单: {cancelled_count} 个")

            return cancelled_count

        except Exception as e:
            logger.error(f"清理过期订单失败: {str(e)}")
            return 0

# ==================== 全局实例 ====================
# 创建全局实例
payment_processor = PaymentProcessor()
game_api_client = GameAPIClient()
shop_manager = ShopManager()
