{"payment": {"appid": "OP10001076", "merchant_no": "82230207372002Z", "term_no": "M4430124", "is_test": false, "notify_url": "http://***************:9000/payment/notify", "return_url": "http://***************:9000/payment/return", "cashier_notify_url": "http://***************:9000/payment/cashier_notify", "cert_dir": "cert", "platform_cert_prod": "lkl-apigw-v1.cer", "platform_cert_test": "lkl-apigw-v2.cer", "merchant_cert": "api_cert.cer", "merchant_key": "api_private_key.pem", "order_expire_minutes": 20, "request_timeout": 30, "max_retries": 3, "supported_pay_types": {"alipay": {"name": "支付宝", "account_type": "ALIPAY", "pay_mode": "ALIPAY", "use_cashier": true}, "wechat": {"name": "微信支付", "account_type": "WECHAT", "pay_mode": "WECHAT", "use_cashier": true}}, "trans_types": {"qrcode": "41", "jsapi": "51", "miniprogram": "71"}}, "game": {"server_host": "127.0.0.1", "server_port": 50000, "api_token": "888888", "add_token_path": "/addtoken", "add_gold_path": "/addgold", "send_item_path": "/sendmail", "check_player_path": "/checkplayer", "request_timeout": 10, "max_retries": 3}, "shop": {"database_path": "shop_orders.db", "order_expire_hours": 24, "player_name_min_length": 2, "player_name_max_length": 20, "player_name_pattern": "^[\\u4e00-\\u9fa5a-zA-Z0-9_]+$"}, "app": {"secret_key": "turtle-server-shop-secret-key-2024", "host": "0.0.0.0", "port": 9000, "debug": false, "log_dir": "logs", "log_level": "INFO"}}