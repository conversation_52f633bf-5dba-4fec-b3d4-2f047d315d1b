# 订单日志说明

## 日志文件格式

订单成功后会自动记录到 `logs/orders_YYYY-MM-DD.json` 文件中，按日期分组。

## 日志数据结构

```json
[
  {
    "timestamp": "2025-08-14 18:39:59",
    "order_no": "ORD2025081418395971812FC1",
    "product_name": "金币充值 - 50金币",
    "product_type": "gold",
    "player_name": "xda",
    "amount": 50,
    "price": 100,
    "price_yuan": "1.00",
    "status": "completed",
    "created_at": "2025-08-14 18:39:59",
    "completed_at": "2025-08-14 18:40:15",
    "payment_info": {
      "api_trade_no": "LKL2025081418401234567",
      "total_amount": "100",
      "buyer": "user123"
    }
  }
]
```

## 字段说明

- `timestamp`: 日志记录时间
- `order_no`: 订单号
- `product_name`: 商品名称
- `product_type`: 商品类型 (gold/token/item)
- `player_name`: 游戏角色名
- `amount`: 商品数量
- `price`: 支付金额（分）
- `price_yuan`: 支付金额（元）
- `status`: 订单状态
- `created_at`: 订单创建时间
- `completed_at`: 订单完成时间
- `payment_info`: 支付相关信息
  - `api_trade_no`: 第三方支付交易号
  - `total_amount`: 支付金额
  - `buyer`: 买家信息

## 日志文件管理

- 日志文件按日期自动分组
- 每个文件包含当天所有成功的订单记录
- JSON格式便于程序处理和人工查看
- 建议定期备份重要的日志文件
