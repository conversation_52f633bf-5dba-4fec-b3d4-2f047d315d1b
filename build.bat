@echo off
chcp 65001 >nul
echo 🐢 乌龟服商城打包脚本
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo 检查PyInstaller...
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo 📦 安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo 检查依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo 清理旧的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo 开始打包...
pyinstaller build.spec
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo ✅ 打包完成！
echo 可执行文件位置: dist\乌龟服商城.exe
echo.
echo 📋 使用说明:
echo 1. 将cert目录下的证书文件复制到exe同目录
echo 2. 修改config.py中的配置（如果需要）
echo 3. 运行exe文件启动服务
echo.
pause
