# 🐢 乌龟服商城 - 优化版

基于拉卡拉支付的魔兽世界私服充值商城系统（优化版）。

## ✨ 功能特性

- ✅ 支持支付宝、微信支付
- ✅ 扫码支付和收银台支付
- ✅ 自动充值到游戏账户
- ✅ 订单管理和状态跟踪
- ✅ 支付回调处理
- ✅ 响应式Web界面
- 🆕 优化的项目结构，文件更少
- 🆕 支持打包成单个exe文件
- 🆕 支持自动获取公网IP配置

## 🚀 快速开始

### 方式一：直接运行（开发模式）

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置证书**
将拉卡拉提供的证书文件放入 `cert/` 目录：
- `api_cert.cer` - 商户证书
- `api_private_key.pem` - 商户私钥
- `lkl-apigw-v1.cer` - 拉卡拉平台证书（生产环境）
- `lkl-apigw-v2.cer` - 拉卡拉平台证书（测试环境）

3. **修改配置**
程序启动时会自动读取 `config.json` 配置文件。如果文件不存在，会自动创建默认配置文件。
编辑 `config.json` 中的关键配置：
```json
{
  "payment": {
    "appid": "your_appid",
    "merchant_no": "your_merchant_no",
    "term_no": "your_term_no",
    "notify_url": "http://your_domain:9000/payment/notify"
  },
  "game": {
    "server_host": "your_game_server_ip",
    "server_port": your_game_server_port
  }
}
```

4. **启动服务**
```bash
python main.py
```

### 方式二：打包成exe（生产模式）

1. **运行打包脚本**
```bash
build.bat
```

2. **配置和启动**
- 将证书文件复制到exe同目录的cert文件夹
- 修改config.json配置文件
- 运行 `乌龟服商城.exe`

## 📁 优化后的项目结构

```
乌龟服商城拉卡拉/
├── main.py                # 主应用（整合所有功能）
├── config.py              # 统一配置文件
├── core.py                # 核心业务逻辑
├── requirements.txt       # 依赖包
├── items_config.json      # 物品配置
├── build.spec             # PyInstaller配置
├── build.bat              # 打包脚本
├── cert/                  # 证书文件
├── templates/             # 页面模板
├── static/                # 静态资源（已合并）
│   ├── app.css           # 合并后的样式文件
│   └── app.js            # 合并后的脚本文件
└── logs/                  # 日志文件
```

## 🔗 主要接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/` | GET | 商城首页 |
| `/api/create_order` | POST | 创建充值订单 |
| `/api/create_item_order` | POST | 创建物品订单 |
| `/pay/<order_no>` | GET | 支付页面 |
| `/payment/notify` | POST | 支付回调 |
| `/admin/orders` | GET | 管理后台 |

## 🔄 支付流程

1. 🛒 用户选择充值类型和金额
2. 📝 填写角色名称，选择支付方式
3. 💳 系统创建订单并生成支付二维码
4. 📱 用户扫码支付
5. 🔔 拉卡拉发送支付通知
6. ✅ 系统验证支付并调用游戏充值接口
7. 🎉 充值完成，更新订单状态

## 📊 订单状态

- `pending` - 待支付
- `paid` - 已支付
- `completed` - 已完成（充值成功）
- `failed` - 失败
- `cancelled` - 已取消

## 🛠️ 详细配置说明

### 📋 配置文件说明

程序启动时会自动读取 `config.json` 配置文件。如果文件不存在，会自动创建默认配置文件。

### 🔧 配置项详解

#### 支付配置 (payment)
```json
{
  "payment": {
    "appid": "OP10001076",                    // 拉卡拉应用ID
    "merchant_no": "82230207372002Z",         // 商户号
    "term_no": "M4430124",                    // 终端号
    "is_test": false,                         // 是否测试模式
    "notify_url": "http://your-domain:9000/payment/notify",           // 支付通知地址 (支持自动IP)
    "return_url": "http://your-domain:9000/payment/return",           // 支付返回地址 (支持自动IP)
    "cashier_notify_url": "http://your-domain:9000/payment/cashier_notify", // 收银台通知地址 (支持自动IP)
    "order_expire_minutes": 20,               // 订单过期时间(分钟)
    "request_timeout": 30,                    // 请求超时时间(秒)
    "max_retries": 3                          // 最大重试次数
  }
}
```

**🚀 自动IP功能说明：**
- 如果配置中的回调地址使用默认IP (***************) 或为空，程序会自动获取公网IP并替换
- 如果配置了自定义域名或IP，程序会直接使用配置的地址
- 自动IP获取失败时，会回退到配置文件中的地址

#### 游戏服务器配置 (game)
```json
{
  "game": {
    "server_host": "127.0.0.1",              // 游戏服务器地址
    "server_port": 50000,                     // 游戏服务器端口
    "api_token": "888888",                    // API访问令牌
    "add_token_path": "/addtoken",            // 充值点券接口路径
    "add_gold_path": "/addgold",              // 充值金币接口路径
    "send_item_path": "/sendmail",            // 发送物品接口路径
    "check_player_path": "/checkplayer",      // 检查玩家接口路径
    "request_timeout": 10,                    // 请求超时时间(秒)
    "max_retries": 3                          // 最大重试次数
  }
}
```

#### 商城配置 (shop)
```json
{
  "shop": {
    "charge_types": {
      "token": {
        "id": "token",
        "name": "点券充值",
        "description": "充值游戏点券",
        "min_amount": 1,                      // 最小充值数量
        "max_amount": 10000,                  // 最大充值数量
        "rate": 1.0,                          // 兑换比例 (1元=1点券)
        "unit": "点券"
      },
      "gold": {
        "id": "gold",
        "name": "金币充值",
        "description": "充值游戏金币",
        "min_amount": 1,
        "max_amount": 10000,
        "rate": 50,                           // 兑换比例 (1元=50金币)
        "unit": "金币"
      }
    },
    "database_path": "shop_orders.db",        // 数据库文件路径
    "order_expire_hours": 24,                 // 订单过期时间(小时)
    "player_name_min_length": 2,              // 玩家名最小长度
    "player_name_max_length": 20,             // 玩家名最大长度
    "player_name_pattern": "^[\\u4e00-\\u9fa5a-zA-Z0-9_]+$"  // 玩家名格式(中文、英文、数字、下划线)
  }
}
```

#### 应用配置 (app)
```json
{
  "app": {
    "secret_key": "turtle-server-shop-secret-key-2024",  // Flask密钥
    "host": "0.0.0.0",                        // 监听地址
    "port": 9000,                             // 监听端口
    "debug": false,                           // 调试模式
    "log_dir": "logs",                        // 日志目录
    "log_level": "INFO"                       // 日志级别
  }
}
```

### 🚀 部署配置

#### 1. 回调地址配置 (支持自动IP)

**方式一：使用自动IP (推荐)**
保持配置文件中的默认IP或留空，程序会自动获取公网IP：
```json
{
  "payment": {
    "notify_url": "http://***************:9000/payment/notify",     // 会自动替换为公网IP
    "return_url": "http://***************:9000/payment/return",     // 会自动替换为公网IP
    "cashier_notify_url": "http://***************:9000/payment/cashier_notify"  // 会自动替换为公网IP
  }
}
```

**方式二：手动指定域名或IP**
如果有固定域名或特定IP，直接配置：
```json
{
  "payment": {
    "notify_url": "http://你的域名:9000/payment/notify",
    "return_url": "http://你的域名:9000/payment/return",
    "cashier_notify_url": "http://你的域名:9000/payment/cashier_notify"
  }
}
```

#### 2. 修改游戏服务器地址
如果游戏服务器不在本机，修改游戏服务器配置：
```json
{
  "game": {
    "server_host": "游戏服务器IP",
    "server_port": 50000
  }
}
```

#### 3. 调整充值比例
根据需要调整充值比例：
```json
{
  "shop": {
    "charge_types": {
      "token": {
        "rate": 1.0    // 1元=1点券
      },
      "gold": {
        "rate": 100    // 1元=100金币
      }
    }
  }
}
```

## ⚠️ 注意事项

- 确保服务器能接收拉卡拉支付通知
- 生产环境建议使用HTTPS
- 定期备份数据库文件 `shop_orders.db`
- 监控日志，及时处理异常
- exe版本需要将证书文件放在同目录的cert文件夹中
- **配置文件编码**：请使用UTF-8编码保存配置文件
- **JSON格式**：配置文件必须是有效的JSON格式，注意逗号和引号
- **重启生效**：修改配置后需要重启程序才能生效
- **备份配置**：建议在修改前备份原配置文件
- **权限设置**：确保程序有读写配置文件的权限

## 🆕 优化内容

- ✅ 合并配置文件：统一使用 `config.json` 配置文件
- ✅ 整合核心模块：所有业务逻辑合并到 `core.py`
- ✅ 单一启动文件：统一使用 `main.py` 启动
- ✅ 合并静态资源：CSS和JS文件各自合并为单个文件
- ✅ 优化项目结构：减少文件数量，便于打包
- ✅ 支持exe打包：提供完整的PyInstaller配置
- ✅ 自动IP获取：支持自动获取公网IP配置回调地址

## 📝 日志文件

- 应用日志: `logs/app.log`
- 订单日志: `logs/orders_YYYY-MM-DD.json`
- 启动日志: `logs/shop_YYYYMMDD.log`

## 🔍 故障排除

### 1. 配置文件问题

如果程序启动失败，请检查：
1. `config.json` 文件格式是否正确
2. 配置项是否完整
3. 文件编码是否为UTF-8
4. 程序是否有读写权限

如果配置文件损坏，删除 `config.json` 文件，程序会自动创建默认配置。

### 2. 端口冲突

```
❌ Address already in use
```

**解决方案**:
- 检查9000端口是否被占用
- 修改config.json中的端口号：`"app": {"port": 其他端口}`

### 3. 游戏API连接失败

```
❌ 游戏API连接失败: 连接失败
```

**解决方案**:
- 确保游戏服务器运行在配置的地址和端口
- 检查防火墙设置
- 验证API token是否正确

### 4. 支付通知验证失败

```
支付通知签名验证失败
```

**解决方案**:
- 检查证书文件是否正确
- 确认回调地址配置正确
- 验证拉卡拉商户配置信息

### 5. 自动IP获取失败

如果自动IP获取失败，程序会使用配置文件中的默认地址。可以：
- 手动配置固定IP或域名
- 检查网络连接
- 查看日志了解具体错误信息

## 📞 技术支持

如有问题，请查看日志文件或联系技术支持。

## 📄 许可证

本项目仅供学习和研究使用。

---

🐢 **乌龟服商城** - 让充值更简单！支持自定义金额，灵活便捷！
